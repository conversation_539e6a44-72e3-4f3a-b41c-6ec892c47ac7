import os
import sys
import tempfile
import Live
from Live import Song
import json
from functools import partial
from typing import Tuple, Any
import time

from .handler import AbletonOSCHandler

class SongHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "song"
        self.logger.warning(f"SongHandler instance created with id: {id(self)}")
        self.last_song_time = 0.0  # Initialisation de last_song_time pour le suivi de la position
        self.last_sent_position = (0, 0, 0, 0.0)  # Stockage de la dernière position envoyée (bar, beat, subdivision, temps)
        self.last_sent_tempo = 0.0  # Stockage du dernier tempo envoyé
        self.last_sent_signature = (0, 0)  # Stockage de la dernière signature rythmique envoyée (numérateur, dénominateur)
        self.position_sync_active = False  # Indique si le suivi de position est actif
        self.start_time = None  # Pour le calcul du temps réel écoulé
        self.time_offset = 0.0  # Offset pour le calcul du temps quand on arrête/reprend la lecture

    def delete_selected_track(self, params=None):
        """Supprime la piste actuellement sélectionnée dans Live.
        
        Cette méthode trouve la piste sélectionnée dans la vue et la supprime.
        Gère à la fois les pistes normales et les return tracks.
        Si aucune piste n'est sélectionnée, un message d'avertissement est enregistré.
        """
        try:
            selected_track = self.song.view.selected_track
            if selected_track:                               
                # Vérifier si c'est une return track
                if selected_track == self.song.master_track:
                    self.logger.warning("Impossible de supprimer la piste maîtresse")
                    return
                if selected_track in self.song.return_tracks:
                    return_track_index = list(self.song.return_tracks).index(selected_track)
                    self.song.delete_return_track(return_track_index)
                    self.logger.info(f"Return track sélectionnée supprimée: index {return_track_index}")
                else:
                    track_index = list(self.song.tracks).index(selected_track)
                    self.song.delete_track(track_index)
                    self.logger.info(f"Piste sélectionnée supprimée: index {track_index}")
            else:
                self.logger.warning("Aucune piste sélectionnée à supprimer")
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de la piste sélectionnée: {str(e)}")

    def init_api(self):
        #--------------------------------------------------------------------------------
        # Callbacks for Song: methods
        #--------------------------------------------------------------------------------
        for method in [
            "capture_midi",
            "continue_playing",
            "create_audio_track",
            "create_midi_track",
            "create_return_track",
            "create_scene",
            "delete_return_track",
            "delete_scene",
            "delete_selected_track",
            "delete_track",
            "duplicate_scene",
            "duplicate_track",
            "jump_by",
            "jump_to_prev_cue",
            "jump_to_next_cue",
            "redo",
            "start_playing",
            "stop_all_clips",
            "stop_playing",
            "tap_tempo",
            "trigger_session_record",
            "undo"
        ]:
            callback = partial(self._call_method, self.song, method)
            self.osc_server.add_handler("/live/song/%s" % method, callback)

        # Handler spécifique pour delete_selected_track qui utilise notre méthode personnalisée
        self.osc_server.add_handler("/live/song/delete_selected_track", self.delete_selected_track)

        #--------------------------------------------------------------------------------
        # Callbacks for Song: properties (read/write)
        #--------------------------------------------------------------------------------
        properties_rw = [
            "arrangement_overdub",
            "back_to_arranger",
            "clip_trigger_quantization",
            "current_song_time",
            "groove_amount",
            "loop",
            "loop_length",
            "metronome",
            "midi_recording_quantization",
            "nudge_down",
            "nudge_up",
            "punch_in",
            "punch_out",
            "record_mode",
            "session_record",
            "signature_denominator",
            "signature_numerator",
            "tempo"
        ]

        #--------------------------------------------------------------------------------
        # Callbacks for Songi: properties (read-only)
        #--------------------------------------------------------------------------------
        properties_r = [
            "can_redo",
            "can_undo",
            "is_playing",
            "song_length",
        ]

        for prop in properties_r + properties_rw:
            self.osc_server.add_handler("/live/song/get/%s" % prop, partial(self._get_property, self.song, prop))
            self.osc_server.add_handler("/live/song/start_listen/%s" % prop, partial(self._start_listen, self.song, prop))
            self.osc_server.add_handler("/live/song/stop_listen/%s" % prop, partial(self._stop_listen, self.song, prop))
        for prop in properties_rw:
            self.osc_server.add_handler("/live/song/set/%s" % prop, partial(self._set_property, self.song, prop))

        #--------------------------------------------------------------------------------
        # Callbacks for Song: Track properties
        #--------------------------------------------------------------------------------
        def song_get_num_tracks(params=None):
            visible_tracks = [track for track in self.song.tracks if track.is_visible]
            num_visible_tracks = len(visible_tracks) + len(self.song.return_tracks) + 1
            return (num_visible_tracks, len(self.song.return_tracks))

        self.osc_server.add_handler("/live/song/get/num_tracks", lambda params: song_get_num_tracks())


        def song_get_track_names(params):
            if len(params) == 0:
                track_index_min, track_index_max = 0, len(self.song.tracks)
            else:
                track_index_min, track_index_max = params
                if track_index_max == -1:
                    track_index_max = len(self.song.tracks)
            return tuple(self.song.tracks[index].name for index in range(track_index_min, track_index_max))
        self.osc_server.add_handler("/live/song/get/track_names", song_get_track_names)

        def song_get_track_data(params):
            """
            Retrieve one more properties of a block of tracks and their clips.
            Properties must be of the format track.property_name or clip.property_name.

            For example:
                /live/song/get/track_data 0 12 track.name clip.name clip.length

            Queries tracks 0..11, and returns a list of values comprising:

            [track_0_name, clip_0_0_name,   clip_0_1_name,   ... clip_0_7_name,
                           clip_1_0_length, clip_0_1_length, ... clip_0_7_length,
             track_1_name, clip_1_0_name,   clip_1_1_name,   ... clip_1_7_name, ...]
            """
            track_index_min, track_index_max, *properties = params
            track_index_min = int(track_index_min)
            track_index_max = int(track_index_max)
            self.logger.info("Getting track data: %s (tracks %d..%d)" %
                             (properties, track_index_min, track_index_max))
            if track_index_max == -1:
                track_index_max = len(self.song.tracks)
            rv = []
            for track_index in range(track_index_min, track_index_max):
                track = self.song.tracks[track_index]
                for prop in properties:
                    obj, property_name = prop.split(".")
                    if obj == "track":
                        if property_name == "num_devices":
                            value = len(track.devices)
                        else:
                            value = getattr(track, property_name)
                            if isinstance(value, Live.Track.Track):
                                #--------------------------------------------------------------------------------
                                # Map Track objects to their track_index to return via OSC
                                #--------------------------------------------------------------------------------
                                value = list(self.song.tracks).index(value)
                        rv.append(value)
                    elif obj == "clip":
                        for clip_slot in track.clip_slots:
                            if clip_slot.clip is not None:
                                rv.append(getattr(clip_slot.clip, property_name))
                            else:
                                rv.append(None)
                    elif obj == "clip_slot":
                        for clip_slot in track.clip_slots:
                            rv.append(getattr(clip_slot, property_name))
                    elif obj == "device":
                        for device in track.devices:
                            rv.append(getattr(device, property_name))
                    else:
                        self.logger.error("Unknown object identifier in get/track_data: %s" % obj)
            return tuple(rv)
        self.osc_server.add_handler("/live/song/get/track_data", song_get_track_data)


        def song_export_structure(params):
            tracks = []
            for track_index, track in enumerate(self.song.tracks):
                group_track = None
                if track.group_track is not None:
                    group_track = list(self.song.tracks).index(track.group_track)
                track_data = {
                    "index": track_index,
                    "name": track.name,
                    "is_foldable": track.is_foldable,
                    "group_track": group_track,
                    "clips": [],
                    "devices": []
                }
                for clip_index, clip_slot in enumerate(track.clip_slots):
                    if clip_slot.clip:
                        clip_data = {
                            "index": clip_index,
                            "name": clip_slot.clip.name,
                            "length": clip_slot.clip.length,
                        }
                        track_data["clips"].append(clip_data)

                for device_index, device in enumerate(track.devices):
                    device_data = {
                        "class_name": device.class_name,
                        "type": device.type,
                        "name": device.name,
                        "parameters": []
                    }
                    for parameter in device.parameters:
                        device_data["parameters"].append({
                            "name": parameter.name,
                            "value": parameter.value,
                            "min": parameter.min,
                            "max": parameter.max,
                            "is_quantized": parameter.is_quantized,
                        })
                    track_data["devices"].append(device_data)

                tracks.append(track_data)
            song = {
                "tracks": tracks
            }

            if sys.platform == "darwin":
                #--------------------------------------------------------------------------------
                # On macOS, TMPDIR by default points to a process-specific directory.
                # We want to use a global temp dir (typically, tmp) so that other processes
                # know where to find this output .json, so unset TMPDIR.
                #--------------------------------------------------------------------------------
                os.environ["TMPDIR"] = ""
            fd = open(os.path.join(tempfile.gettempdir(), "abletonosc-song-structure.json"), "w")
            json.dump(song, fd)
            fd.close()
            return (1,)
        self.osc_server.add_handler("/live/song/export/structure", song_export_structure)

        #--------------------------------------------------------------------------------
        # Callbacks for Song: Scene properties
        #--------------------------------------------------------------------------------
        self.osc_server.add_handler("/live/song/get/num_scenes", lambda _: (len(self.song.scenes),))

        def song_get_scene_names(params):
            if len(params) == 0:
                scene_index_min, scene_index_max = 0, len(self.song.scenes)
            else:
                scene_index_min, scene_index_max = params
            return tuple(self.song.scenes[index].name for index in range(scene_index_min, scene_index_max))
        self.osc_server.add_handler("/live/song/get/scene_names", song_get_scene_names)

        #--------------------------------------------------------------------------------
        # Callbacks for Song: Cue point properties
        #--------------------------------------------------------------------------------
        def song_get_cue_points(song, _):
            cue_points = song.cue_points
            cue_point_pairs = [(cue_point.name, cue_point.time) for cue_point in cue_points]
            return tuple(element for pair in cue_point_pairs for element in pair)
        self.osc_server.add_handler("/live/song/get/cue_points", partial(song_get_cue_points, self.song))

        def song_jump_to_cue_point(song, params: Tuple[Any] = ()):
            cue_point_index = params[0]
            if isinstance(cue_point_index, str):
                for cue_point in song.cue_points:
                    if cue_point.name == cue_point_index:
                        cue_point.jump()
            elif isinstance(cue_point_index, int):
                cue_point = song.cue_points[cue_point_index]
                cue_point.jump()
        self.osc_server.add_handler("/live/song/cue_point/jump", partial(song_jump_to_cue_point, self.song))

        #--------------------------------------------------------------------------------
        # Listener for /live/song/get/beat
        #--------------------------------------------------------------------------------
        self.last_song_time = -1.0
        
        def stop_beat_listener(params: Tuple[Any] = ()):
            try:
                self.song.remove_current_song_time_listener(self.current_song_time_changed)
                self.logger.info("Removing beat listener")
            except:
                pass

        def start_beat_listener(params: Tuple[Any] = ()):
            stop_beat_listener()
            self.logger.info("Adding beat listener")
            self.song.add_current_song_time_listener(self.current_song_time_changed)

        self.osc_server.add_handler("/live/song/start_listen/beat", start_beat_listener)
        self.osc_server.add_handler("/live/song/stop_listen/beat", stop_beat_listener)

        # Ajouter les gestionnaires OSC pour la position
        self.osc_server.add_handler("/live/song/start_listen/position", self.start_song_position_listener)
        self.osc_server.add_handler("/live/song/stop_listen/position", self.stop_song_position_listener)
        
        # Ajouter le gestionnaire pour les mesures de latence
        self.osc_server.add_handler("/live/song/ping", self.request_latency_ping)

        # Ajouter le gestionnaire pour les nouvelles fonctions toggle
        self.osc_server.add_handler("/live/song/toggle_record", self.toggle_record)
        self.osc_server.add_handler("/live/song/toggle_loop", self.toggle_loop)
        self.osc_server.add_handler("/live/song/toggle_punch_in", self.toggle_punch_in)
        self.osc_server.add_handler("/live/song/toggle_punch_out", self.toggle_punch_out)

        # Ajouter le gestionnaire pour envoyer l'état complet de la chanson
        self.osc_server.add_handler("/live/song/get/state", self.send_song_state)

        # Ajouter les listeners pour les changements d'état de la chanson
        self.song.add_is_playing_listener(self.send_song_state)
        self.song.add_record_mode_listener(self.send_song_state)
        self.song.add_metronome_listener(self.send_song_state)
        self.song.add_loop_listener(self.send_song_state)
        self.song.add_punch_in_listener(self.send_song_state)
        self.song.add_punch_out_listener(self.send_song_state)

        # Handler spécifique pour set_or_delete_cue
        self.osc_server.add_handler("/live/song/set_or_delete_cue", self.set_or_delete_cue)

        # Ajouter le gestionnaire spécifique pour /live/song/set/loop_start
        self.osc_server.add_handler("/live/song/set/loop_start", self.set_loop_start_to_current_position)

        # Ajouter un listener pour les changements de points cue (peut ne pas se déclencher dans tous les cas)
        self.song.add_cue_points_listener(self._send_all_cue_point_names)

        # Envoyer la liste initiale des points cue à l'initialisation
        self._send_all_cue_point_names(self.song.cue_points)

    def start_song_position_listener(self, params: Tuple[Any] = ()):
        """Start listening to song position changes."""
        # Arrêter d'abord tout listener existant
        self.stop_song_position_listener()
        
        # Activation du suivi de position
        self.position_sync_active = True
        self.start_time = None
        # Initialiser l'offset avec la position actuelle
        self.time_offset = self.song.current_song_time / 1000.0
        
        # Envoyer la position initiale immédiatement avec contexte complet
        self._send_full_position_sync()
        
        self.logger.info("Démarrage du listener de position de chanson")
        self.song.add_is_playing_listener(self.on_is_playing_changed)
        self.song.add_tempo_listener(self._send_full_position_sync)
        self.song.add_signature_numerator_listener(self._send_full_position_sync)
        self.song.add_signature_denominator_listener(self._send_full_position_sync)
        self.song.add_current_song_time_listener(self.song_position_changed)

    def stop_song_position_listener(self, params: Tuple[Any] = ()):
        """Stop listening to song position changes."""
        if self.position_sync_active:
            self.position_sync_active = False
            self.song.remove_is_playing_listener(self.on_is_playing_changed)
            self.song.remove_tempo_listener(self._send_full_position_sync)
            self.song.remove_signature_numerator_listener(self._send_full_position_sync)
            self.song.remove_signature_denominator_listener(self._send_full_position_sync)
            self.song.remove_current_song_time_listener(self.song_position_changed)

    def song_position_changed(self):
        """Appelé à chaque changement de temps dans la chanson."""
        # Ne rien faire si le suivi n'est pas actif
        if not self.position_sync_active:
            return
            
        # Récupérer la position en BeatTime
        beats_time = self.song.get_current_beats_song_time()
        
        # Récupérer le temps en millisecondes et convertir en secondes
        exact_time = self.get_current_time()
        
        # Accéder correctement aux attributs de BeatTime
        bar = beats_time.bars
        beat = beats_time.beats
        subdivision = beats_time.sub_division
        
        # Si on est en lecture, on ne veut envoyer qu'une fois par beat
        if self.song.is_playing:
            # Vérifier si on a changé de beat
            current_beat_position = (bar, beat)
            last_beat_position = (self.last_sent_position[0], self.last_sent_position[1])
            
            if current_beat_position == last_beat_position:
                return  # On n'envoie pas si on est toujours sur le même beat
        
        # Créer un tuple avec la position actuelle (mesure, temps, subdivision, temps_exact)
        current_position = (bar, beat, subdivision, exact_time)
        
        # Envoyer un message compact pour les mises à jour régulières
        self.osc_server.send("live/sp/beats", current_position)
        self.last_sent_position = current_position
        self.logger.debug(f"Position mise à jour: bars={bar}, beats={beat}, sub={subdivision}, time={exact_time}")
        
        # Envoyer le nom du point cue actuel
        self._send_current_cue_name()
            
    def on_is_playing_changed(self):
        """Appelé quand la lecture démarre ou s'arrête."""
        if not self.position_sync_active:
            return
            
        if self.song.is_playing:
            # Si on démarre la lecture, on met à jour le temps de départ
            # en utilisant la position actuelle comme offset
            self.time_offset = self.song.current_song_time / 1000.0
            self.start_time = time.time()
        else:
            # Si on arrête la lecture, on garde la position actuelle
            if self.start_time is not None:
                self.time_offset = self.song.current_song_time / 1000.0 
            self.start_time = None
            
        # Envoyer une synchronisation complète
        self._send_full_position_sync()
        
    def get_current_time(self):
        """Calcule le temps écoulé total en secondes."""
        # On utilise le format SMPTE 25fps qui est standard en Europe
        time_ms = self.song.get_current_smpte_song_time(Live.Song.TimeFormat.smpte_25)
        
        # Debug des attributs disponibles
        self.logger.info(f"Type de time_ms: {type(time_ms)}")
        self.logger.info(f"Attributs disponibles: {dir(time_ms)}")
        self.logger.info(f"Valeurs: hours={time_ms.hours}, minutes={time_ms.minutes}, seconds={time_ms.seconds}, frames={time_ms.frames}")
        
        # Conversion en secondes (25 frames = 1 seconde)
        return round((time_ms.hours * 3600 + time_ms.minutes * 60 + time_ms.seconds + time_ms.frames / 25.0), 2)

    def _send_full_position_sync(self):
        """Envoie une synchronisation complète avec toutes les informations nécessaires
        pour que le destinataire puisse calculer précisément la position."""
        if not self.position_sync_active:
            return
            
        # Position en beats
        beats_time = self.song.get_current_beats_song_time()
        bar = beats_time.bars
        beat = beats_time.beats               
        subdivision = beats_time.sub_division
        
        # Informations de tempo et signature
        tempo = self.song.tempo
        signature_num = self.song.signature_numerator
        signature_denom = self.song.signature_denominator
        
        # Temps absolu en millisecondes converti en secondes
        song_time = self.get_current_time()
        
        # État de lecture
        is_playing = 1 if self.song.is_playing else 0
        
        # Mise à jour des dernières valeurs envoyées
        self.last_sent_position = (bar, beat, subdivision)
        self.last_sent_tempo = tempo
        self.last_sent_signature = (signature_num, signature_denom)       
        
        # Envoyer un message avec toutes les informations
        self.osc_server.send("live/sp/sync", (
            bar, beat, subdivision,       # Position actuelle avec subdivision
            song_time,                    # Temps absolu dans la chanson
            tempo,                        # Tempo en BPM
            signature_num, signature_denom, # Signature rythmique
            is_playing                   # État de lecture                     
        ))
        
        self.logger.info(f"Synchronisation complète: bars={bar}, beats={beat}, sub={subdivision}, "
                         f"tempo={tempo}, signature={signature_num}/{signature_denom}, playing={is_playing}")

        # Envoyer le nom du point cue actuel
        self._send_current_cue_name()

    def clear_api(self):
        super().clear_api()
        try:
            self.song.remove_current_song_time_listener(self.current_song_time_changed)
        except:
            pass
            
        # Suppression des listeners de position de chanson
        try:
            self.song.remove_current_song_time_listener(self.song_position_changed)
        except:
            pass
            
        # Suppression du listener is_playing
        try:
            if self.song.is_playing_has_listener(self.on_is_playing_changed):
                self.song.remove_is_playing_listener(self.on_is_playing_changed)
        except:
            pass
            
        # Suppression des listeners de tempo et signature
        try:
            self.song.remove_tempo_listener(self.on_tempo_changed)
        except:
            pass
            
        try:
            self.song.remove_signature_numerator_listener(self.on_signature_changed)
            self.song.remove_signature_denominator_listener(self.on_signature_changed)
        except:
            pass

        # Suppression des listeners d'état de la chanson
        try:
            self.song.remove_is_playing_listener(self.send_song_state)
            self.song.remove_record_mode_listener(self.send_song_state)
            self.song.remove_metronome_listener(self.send_song_state)
            self.song.remove_loop_listener(self.send_song_state)
            self.song.remove_punch_in_listener(self.send_song_state)
            self.song.remove_punch_out_listener(self.send_song_state)
        except:
            pass

        # Ajouter un listener pour les changements de points cue
        self.song.remove_cue_points_listener(self._send_all_cue_point_names)

    # Ajouter une nouvelle méthode pour mesurer et compenser la latence
    def request_latency_ping(self, params: Tuple[Any] = ()):
        """Gestionnaire pour les requêtes de mesure de latence.
        Quand un client envoie une demande de ping, on répond immédiatement avec un timestamp.
        """
        timestamp_ms = int(time.time() * 1000)
        
        # Si un identifiant a été fourni dans la requête, on le retourne pour que
        # le client puisse associer la réponse à sa demande
        client_id = params[0] if len(params) > 0 else 0
        
        # Envoyer la réponse de ping avec le timestamp actuel
        self.osc_server.send("/sp/ping", (client_id, timestamp_ms))
        
        self.logger.debug(f"Ping répondu au client {client_id} avec timestamp {timestamp_ms}")

    # Nouvelle méthode pour basculer l'état d'enregistrement global (record_mode)
    def toggle_record(self, params=None):
        """Bascule l'état de record_mode (enregistrement global)."""
        current_state = self.song.record_mode
        self.song.record_mode = not current_state
        self.logger.info(f"État de record_mode basculé sur: {self.song.record_mode}")
        self.osc_server.send("/live/song/record_mode", (self.song.record_mode,))

    # Nouvelle méthode pour basculer l'état de loop
    def toggle_loop(self, params=None):
        """Bascule l'état de loop."""
        current_state = self.song.loop
        self.song.loop = not current_state
        self.logger.info(f"État de loop basculé sur: {self.song.loop}")
        self.osc_server.send("/live/song/loop", (self.song.loop,))

    # Nouvelle méthode pour basculer l'état de punch_in
    def toggle_punch_in(self, params=None):
        """Bascule l'état de punch_in."""
        current_state = self.song.punch_in
        self.song.punch_in = not current_state
        self.logger.info(f"État de punch_in basculé sur: {self.song.punch_in}")
        self.osc_server.send("/live/song/punch_in", (self.song.punch_in,))

    # Nouvelle méthode pour basculer l'état de punch_out
    def toggle_punch_out(self, params=None):
        """Bascule l'état de punch_out."""
        current_state = self.song.punch_out
        self.song.punch_out = not current_state
        self.logger.info(f"État de punch_out basculé sur: {self.song.punch_out}")
        self.osc_server.send("/live/song/punch_out", (self.song.punch_out,))

    # Nouvelle méthode pour envoyer l'état complet de la chanson
    def send_song_state(self, params=None):
        """Envoie l'état actuel de la chanson (is_playing, record_mode, metronome, loop, punch_in, punch_out)."""
        is_playing = 1 if self.song.is_playing else 0
        record_mode = 1 if self.song.record_mode else 0
        metronome = 1 if self.song.metronome else 0
        loop = 1 if self.song.loop else 0
        punch_in = 1 if self.song.punch_in else 0
        punch_out = 1 if self.song.punch_out else 0

        # Envoyer l'état complet via OSC
        self.osc_server.send("/live/song/state", (
            is_playing,
            record_mode,
            metronome,
            loop,
            punch_in,
            punch_out
        ))
        self.logger.info(f"État de la chanson envoyé: is_playing={is_playing}, record_mode={record_mode}, metronome={metronome}, loop={loop}, punch_in={punch_in}, punch_out={punch_out}")

    def set_or_delete_cue(self, params=None):
        """Appelle la méthode set_or_delete_cue de l'objet Song."""
        try:
            self.song.set_or_delete_cue()
            self.logger.info("set_or_delete_cue appelé avec succès.")
            # Après modification (ajout/suppression), renvoyer la liste mise à jour des points cue
            self._send_all_cue_point_names(self.song.cue_points)
        except Exception as e:
            self.logger.error(f"Erreur lors de l'appel de set_or_delete_cue : {str(e)}")

    def set_loop_start_to_current_position(self, params=None):
        """Définit la position de départ de la boucle à la position actuelle du curseur de lecture."""
        try:
            current_beats = self.song.current_song_time
            self.song.loop_start = current_beats
            self.logger.info(f"Début de boucle défini à la position actuelle du curseur: {current_beats} beats.")
            # Optionnel : Envoyer la nouvelle valeur via OSC pour confirmation si nécessaire
            self.osc_server.send("/live/song/loop_start", (current_beats,))
        except Exception as e:
            self.logger.error(f"Erreur lors de la définition du début de boucle: {str(e)}")

    def _send_current_cue_name(self):
        """Envoie le nom du point cue actuel."""
        cue_name = "Add Cue" # s'affichera si aucun point cue n'est sélectionné sur le label de l'UI et permettra de créer un nouveau cue point.
        if self.song.is_cue_point_selected():
            current_time = self.song.current_song_time
            for cue in self.song.cue_points:
                # Comparer le temps du cue point avec le temps actuel de la chanson
                # Live API uses floating point for time, direct comparison might be an issue
                # Comparing with a small tolerance might be safer, but let's try direct equality first
                # based on the typical behavior of Live API
                if abs(cue.time - current_time) < 0.001: # Use a small tolerance for float comparison
                    cue_name = cue.name
                    break
            self.logger.info(f"Point cue sélectionné: {cue_name}")
        else:
             self.logger.debug("Aucun point cue sélectionné à la position actuelle.")

        # Envoyer le nom du point cue via OSC
        self.osc_server.send("/live/song/current_cue_name", (cue_name,))

    def _send_all_cue_point_names(self, cue_points):
        """Envoie les noms des points cue actuellement sélectionnés."""
        cue_names = [cue.name for cue in cue_points]
        self.logger.info(f"Points cue sélectionnés: {cue_names}")
        self._send_current_cue_name()
        self.osc_server.send("/live/song/cue_point_names", (cue_names,))
