package livetrackmode

// Constantes pour le mode Track
const (
	SlotsPerPage = 8
	SendsPerPage = 4

	// --- Adresses OSC --- Basé sur BACKGROUND_LISTEN_ADDRESSES de liveTrackMode.js

	// Adresses pour les paramètres de la piste sélectionnée (préfixe: /live/trackMode/get/)
	OscAddressSelectedTrackVolume       = "/live/trackMode/get/volume"
	OscAddressSelectedTrackColor        = "/live/trackMode/get/color"
	OscAddressSelectedTrackName         = "/live/trackMode/get/name"
	OscAddressSelectedTrackPanning      = "/live/trackMode/get/panning"
	OscAddressSelectedTrackMute         = "/live/trackMode/get/mute"
	OscAddressSelectedTrackSolo         = "/live/trackMode/get/solo"
	OscAddressSelectedTrackMutedViaSolo = "/live/trackMode/get/muted_via_solo"
	OscAddressSelectedTrackArm          = "/live/trackMode/get/arm"
	OscAddressSelectedTrackSends        = "/live/trackMode/get/sends"
	OscAddressSelectedTrackDaughter     = "/live/trackMode/select/daughter"
	OscAddressSelectedTrackSister       = "/live/trackMode/select/sister"
	OscAddressSelectedTrackParent       = "/live/trackMode/select/parent"

	// Adresses pour les paramètres de la piste verrouillée (préfixe: /live/trackLockMode/get/)
	OscAddressLockedTrackVolume       = "/live/trackLockMode/get/volume"
	OscAddressLockedTrackColor        = "/live/trackLockMode/get/color"
	OscAddressLockedTrackName         = "/live/trackLockMode/get/name"
	OscAddressLockedTrackPanning      = "/live/trackLockMode/get/panning"
	OscAddressLockedTrackMute         = "/live/trackLockMode/get/mute"
	OscAddressLockedTrackSolo         = "/live/trackLockMode/get/solo"
	OscAddressLockedTrackMutedViaSolo = "/live/trackLockMode/get/muted_via_solo"
	OscAddressLockedTrackArm          = "/live/trackLockMode/get/arm"
	OscAddressLockedTrackSends        = "/live/trackLockMode/get/sends"
	OscAddressLockedTrackDaughter     = "/live/trackLockMode/select/daughter"
	OscAddressLockedTrackSister       = "/live/trackLockMode/select/sister"
	OscAddressLockedTrackParent       = "/live/trackLockMode/select/parent"

	// Adresses pour les relations entre pistes (mode normal)
	OscAddressTrackModeDaughters = "/live/trackMode/daughters"
	OscAddressTrackModeSisters   = "/live/trackMode/sisters"
	OscAddressTrackModeParent    = "/live/trackMode/parent"

	// Adresses pour les relations entre pistes (mode verrouillé)
	OscAddressTrackLockModeDaughters = "/live/trackLockMode/daughters"
	OscAddressTrackLockModeSisters   = "/live/trackLockMode/sisters"
	OscAddressTrackLockModeParent    = "/live/trackLockMode/parent"

	// Adresses pour la synchronisation
	OscAddressSync               = "live/sp/sync"
	OscAddressBeats              = "live/sp/beats"
	OscAddressSongState          = "/live/song/state"
	OscAddressSongCurrentCueName = "/live/song/current_cue_name"

	// Autres adresses OSC pour le mode Track
	OscAddressStartListenTrackMode     = "/live/start_listen_trackMode"
	OscAddressStartListenTrackLockMode = "/live/start_listen_trackLockMode"
	OscAddressTrackLock                = "/live/track_lock"
	OscAddressTrackUnlock              = "/live/track_unlock"
	OscAddressUpdateTrackMode          = "/live/update_trackMode"

	// Adresses OSC d'envoi (à compléter/affiner)
	OscAddressTrackSetVolume          = "/live/track/set/volume"
	OscAddressTrackSetPanning         = "/live/track/set/panning"
	OscAddressTrackSetSends           = "/live/track/set/sends"
	OscAddressTrackSetMuteToggle      = "/live/track/set/mutetoggle"
	OscAddressTrackSetSoloToggle      = "/live/track/set/solotoggle"
	OscAddressTrackSetArmToggle       = "/live/track/set/armtoggle"
	OscAddressSongStartListenPosition = "/live/song/start_listen/position"
	OscAddressSongStopListenPosition  = "/live/song/stop_listen/position"

	// Préfixes de messages hardware (à compléter/affiner)
	HardwareMessagePrefixTrackQuickView = "tq" // tq0/tq1
	HardwareMessagePrefixMode           = "mo" // mo,0/mo,1
	HardwareMessagePrefixTrackSlot      = "ts" // tsA, tsB, etc.
	HardwareMessagePrefixReturnName     = "rn" // rn,nul,nul,nul,nul
	HardwareMessagePrefixLockState      = "lt" // lt00/lt01

	// Alias pour la compatibilité avec le code existant
	// Ces constantes sont maintenues pour éviter de casser le code existant
	// mais devraient être remplacées progressivement par les nouvelles constantes
	OscAddressTrackVolumeGet           = OscAddressSelectedTrackVolume
	OscAddressTrackColorGet            = OscAddressSelectedTrackColor
	OscAddressTrackNameGet             = OscAddressSelectedTrackName
	OscAddressTrackPanningGet          = OscAddressSelectedTrackPanning
	OscAddressTrackMuteGet             = OscAddressSelectedTrackMute
	OscAddressTrackSoloGet             = OscAddressSelectedTrackSolo
	OscAddressTrackMutedViaSoloGet     = OscAddressSelectedTrackMutedViaSolo
	OscAddressTrackArmGet              = OscAddressSelectedTrackArm
	OscAddressTrackSendsGet            = OscAddressSelectedTrackSends
	OscAddressTrackLockVolumeGet       = OscAddressLockedTrackVolume
	OscAddressTrackLockColorGet        = OscAddressLockedTrackColor
	OscAddressTrackLockNameGet         = OscAddressLockedTrackName
	OscAddressTrackLockPanningGet      = OscAddressLockedTrackPanning
	OscAddressTrackLockMuteGet         = OscAddressLockedTrackMute
	OscAddressTrackLockSoloGet         = OscAddressLockedTrackSolo
	OscAddressTrackLockMutedViaSoloGet = OscAddressLockedTrackMutedViaSolo
	OscAddressTrackLockArmGet          = OscAddressLockedTrackArm
	OscAddressTrackLockSendsGet        = OscAddressLockedTrackSends
)

// EncoderConfig structure pour les configurations d'encodeur
type EncoderConfig struct {
	Min     float64
	Max     float64
	MinStep float64
	MaxStep float64
}

// Définir les configurations d'encodeur (similaire au JS encoderConfig)
var EncoderConfigVolume = EncoderConfig{
	Min:     0.0,
	Max:     1.0,
	MinStep: 0.001,
	MaxStep: 0.025,
}

var EncoderConfigPanning = EncoderConfig{
	Min:     -1.0,
	Max:     1.0,
	MinStep: 0.002,
	MaxStep: 0.05,
}

var EncoderConfigSend = EncoderConfig{
	Min:     0.0,
	Max:     1.0, // Les sends vont généralement de 0 à 1
	MinStep: 0.001,
	MaxStep: 0.025,
}

// TrackData représente les données d'une piste dans Live
type TrackData struct {
	TrackIdx             int
	Volume               float64
	Panning              float64
	Sends                []float64
	Name                 string
	Color                string // Stocké comme entier décimal en chaîne
	Mute                 bool
	MutedViaSolo         bool
	Solo                 bool
	Arm                  int // Changer bool pour int (0=off, 1=on, 2=auto?)
	TrackIsFoldable      bool
	TrackFoldState       bool
	TrackIsGrouped       bool
	DisplayIndex         string   // Index affiché (numéro ou lettre)
	FormattedTrackNumber string   // Ex: "track 1/10"
	ParentName           string   // Nom de la piste parente
	DaughterNames        []string // Noms des pistes filles
	SisterNames          []string // Noms des pistes soeurs
	// Ajoutez d'autres champs si nécessaire
}

// DefaultTrackData retourne une instance de TrackData avec les valeurs par défaut
func DefaultTrackData() *TrackData {
	return &TrackData{
		Volume:               0,
		Panning:              0,
		Sends:                []float64{}, // Initialiser comme un slice vide
		Name:                 "",
		Color:                "0", // Couleur par défaut (noir?)
		Mute:                 false,
		MutedViaSolo:         false,
		Solo:                 false,
		Arm:                  0, // Valeur par défaut pour arm
		TrackIsFoldable:      false,
		TrackFoldState:       false,
		TrackIsGrouped:       false,
		DisplayIndex:         "",
		FormattedTrackNumber: "",
		ParentName:           "",
		DaughterNames:        []string{},
		SisterNames:          []string{},
	}
}
