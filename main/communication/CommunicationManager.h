// CommunicationManager.h

#ifndef COMMUNICATION_MANAGER_H
#define COMMUNICATION_MANAGER_H

#include "SerialManager.h"
#include <functional>
#include <map>
#include <string>
#include <cstring>
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Inclusion pour l'écran de démarrage
#include "../scr_start/scr_start.h"

// Taille du buffer circulaire (fixée à la compilation)
#define CIRCULAR_BUFFER_SIZE 4000

// Structure pour stocker temporairement les données des devices
struct DeviceData
{
    char **names;
    int *states;
    int count;
    int selectedIndex;
    int currentIndex;

    DeviceData(int maxCount);
    ~DeviceData();
};

// Déclaration des variables globales
extern DeviceData *currentDeviceData;
extern DeviceData *currentRackDeviceData;
extern DeviceData *currentChainData;

// Structure pour bufferiser les mises à jour des paramètres
struct UpdateRequest
{
    char container_letter;
    int32_t value;
    std::string value_string;
    uint64_t timestamp; // Utilisation de uint64_t pour esp_timer
    bool pending;
};

class CommunicationManager
{
public:
    CommunicationManager(long serialBaudRate);
    void init(const char *deviceName);
    void sendMessage(const std::string &message);
    std::string receiveMessage();
    void update();
    bool messageAvailable();
    void clearMessageBuffer();

private:
    static constexpr uint32_t BASE_PROCESS_DELAY = 2;
    static constexpr uint32_t CHAR_PROCESS_DELAY = 1;
    static constexpr uint32_t MAX_PROCESS_DELAY = 15;
    uint64_t lastProcessTime = 0; // Utilisation de uint64_t pour esp_timer

    // Buffer pour les mises à jour différées des paramètres
    static constexpr int UPDATE_BUFFER_SIZE = 8; // Taille basée sur l'ancien code (A-H)
    UpdateRequest updateBuffer[UPDATE_BUFFER_SIZE];
    static constexpr uint32_t UPDATE_DELAY_MS = 5; // Délai en ms avant d'appliquer la mise à jour

    struct CircularBuffer
    {
        char buffer[CIRCULAR_BUFFER_SIZE];
        size_t head;
        size_t tail;
        bool full;

        CircularBuffer() : head(0), tail(0), full(false)
        {
            memset(buffer, 0, CIRCULAR_BUFFER_SIZE);
        }

        bool push(char c)
        {
            if (full) return false;
            buffer[head] = c;
            head = (head + 1) % CIRCULAR_BUFFER_SIZE;
            full = (head == tail);
            return true;
        }

        bool pushString(const char *str, size_t len)
        {
            for (size_t i = 0; i < len; i++)
            {
                if (!push(str[i])) return false;
            }
            return true;
        }

        bool popMessage(char *dest, size_t maxLen)
        {
            if (isEmpty()) return false;

            bool messageComplete = false;
            size_t checkPos = tail;
            size_t messageLength = 0;

            while (checkPos != head && messageLength < maxLen - 1)
            {
                char c = buffer[checkPos];
                if (c == '\0' || c == '\n')
                {
                    messageComplete = true;
                    break;
                }
                messageLength++;
                checkPos = (checkPos + 1) % CIRCULAR_BUFFER_SIZE;
            }

            if (!messageComplete) return false;

            size_t i = 0;
            while (i < maxLen - 1 && tail != head)
            {
                char c = buffer[tail];
                dest[i++] = c;
                tail = (tail + 1) % CIRCULAR_BUFFER_SIZE;
                full = false;

                if (c == '\0' || c == '\n') break;
            }

            dest[i] = '\0';
            return true;
        }

        bool isEmpty() const { return !full && (head == tail); }

        bool hasCompleteMessage() const
        {
            if (isEmpty()) return false;

            size_t current = tail;
            while (current != head)
            {
                if (buffer[current] == '\0' || buffer[current] == '\n')
                    return true;
                current = (current + 1) % CIRCULAR_BUFFER_SIZE;
            }
            return false;
        }

        void clear()
        {
            head = 0;
            tail = 0;
            full = false;
            memset(buffer, 0, CIRCULAR_BUFFER_SIZE);
        }

        size_t getMessageLength() const
        {
            if (isEmpty()) return 0;

            size_t length = 0;
            size_t tempRead = tail;

            while (length < CIRCULAR_BUFFER_SIZE)
            {
                if (tempRead == head) return length;
                if (buffer[tempRead] == '\n') return length + 1;
                length++;
                tempRead = (tempRead + 1) % CIRCULAR_BUFFER_SIZE;
            }
            return length;
        }

        void debugBuffer() const
        {
            if (isEmpty()) {
                ESP_LOGI("CircularBuffer", "Buffer vide");
                return;
            }

            char debugStr[101] = {0};
            size_t current = tail;
            int count = 0;

            while (current != head && count < 100) {
                char c = buffer[current];
                if (c >= 32 && c <= 126) {
                    debugStr[count] = c;
                } else {
                    debugStr[count] = '?';
                }
                current = (current + 1) % CIRCULAR_BUFFER_SIZE;
                count++;
            }

            ESP_LOGI("CircularBuffer", "Contenu du buffer: [%s] (%d%%)",
                     debugStr, getBufferUsage());
        }

        int getBufferUsage() const
        {
            if (full) return 100;
            if (isEmpty()) return 0;

            int usage;
            if (head >= tail) {
                usage = ((head - tail) * 100) / CIRCULAR_BUFFER_SIZE;
            } else {
                usage = ((CIRCULAR_BUFFER_SIZE - tail + head) * 100) / CIRCULAR_BUFFER_SIZE;
            }
            return usage;
        }
    };

    struct TrackModeData
    {
        bool is_foldable = false;
        bool fold_state = false;
        bool is_grouped = false;
        int muteState = 0;
        int soloState = 0;
        int mutedViaSoloState = 0;
        int armState = 0;
        int color = 0;
        std::string name = "";
        int volume = 0;
        std::string volume_db = "";
        bool isValid = false;
        std::string formattedTrackNumber = "";
        std::string displayIndex = "";
    };

    struct SlotModeData
    {
        std::string name = "";
        bool is_foldable = false;
        bool fold_state = false;
        bool is_grouped = false;
        int mute_state = 0;
        int arm_state = 0;
        int color = 0;
        int value = 0;
        int slotNumber = -1;
        std::string value_string = "";
        bool isValid = false;
    };

    SerialCommunication serialManager;
    std::function<void(const char *)> sendFunc;
    CircularBuffer messageBuffer;

    using MessageHandler = void (CommunicationManager::*)(const std::string &);
    std::map<std::string, MessageHandler> messageHandlers;

    void sendSerial(const char *msg);
    void updateSendMethod();
    void handleConnectionState();
    void processBufferedUpdates();

    bool addToBuffer(const char *message, size_t length);
    bool getMessageFromBuffer(char *dest, size_t maxLen);
    bool hasMessageInBuffer();

    void initializeMessageHandlers();

    // Handlers de messages (déclarations)
    void handlePanMessage(const std::string &message);
    void handleTrackModeMessage(const std::string &message);
    void handleVolumeMessage(const std::string &message);
    void handleNameMessage(const std::string &message);
    void handleSelectionMessage(const std::string &message);
    void handleDevicePageMessage(const std::string &message);
    void handleParameterNameMessage(const std::string &message);
    void handleParameterValueMessage(const std::string &message);
    void handleLockDeviceMessage(const std::string &message);
    void handleColorMessage(const std::string &message);
    void handleEnableSlotMessage(const std::string &message);
    void handleMuteMessage(const std::string &message);
    void handleMuteSoloMessage(const std::string &message);
    void handleSoloMessage(const std::string &message);
    void handleArmMessage(const std::string &message);
    void handleFoldableMessage(const std::string &message);
    void handleSlotInitMessage(const std::string &message);
    void handlePageVolumeMessage(const std::string &message);
    void handleTrackMutMessage(const std::string &message);
    void handleTrackSoloMessage(const std::string &message);
    void handleTrackVolumeMessage(const std::string &message);
    void handleTrackColorMessage(const std::string &message);
    void handleTrackNameMessage(const std::string &message);
    void handleTrackSendMessage(const std::string &message);
    void handleTrackReturnNameMessage(const std::string &message);
    void handleReturnTracksNameMessage(const std::string &message);
    void handleLearnSlotMessage(const std::string &message);
    void handleLearnValueMessage(const std::string &message);
    void handleSwitchModeMessage(const std::string &message);
    void handleLearnColorMessage(const std::string &message);
    void handleLearnNameMessage(const std::string &message);
    void handleLearnPageMessage(const std::string &message);
    void handleLearnDeviceNameMessage(const std::string &message);
    void handleDeviceBulkParametersDeviceNames(const std::string &message);
    void handleDeviceBulkParametersValues(const std::string &message);
    void handleLearnSlotPropertiesMessage(const std::string &message);
    void handleExitModeMessage(const std::string &message);
    void handleTrackStateViewMessage(const std::string &message);
    void handleDeviceModeTrackPropertiesMessage(const std::string &message);
    void handleDeviceModeIsActiveMessage(const std::string &message);
    void handleBrowserInfoMessage(const std::string &message);
    void handleBrowserItemsFirstBatch(const std::string &message);
    void handleBrowserItemsSecondBatch(const std::string &message);
    void handleBrowserItemsBatch(const std::string &message, int startIndex);
    void handleBrowserPreviewMessage(const std::string &message);
    void handleDevicesRootDevicesMessage(const std::string &message);
    void handleDevicesRackDevicesDisplayMessage(const std::string &message);
    void handleDevicesRackChainsDisplayMessage(const std::string &message);
    void handleDevicesDrumRackChainsDisplayMessage(const std::string &message);
    void handleDeviceModePathStringMessage(const std::string &message);
    void handleLearnClearAllSlotsMessage(const std::string &message);
    void handleDevicesRootDevicesBatchMessage(const std::string &message);
    void handleDeviceModeRemoveDeviceContainerMessage(const std::string &message);
    void handleDeviceModeAddDeviceContainerMessage(const std::string &message);
    void handleDevicesRackDevicesMessage(const std::string &message);
    void handleDevicesRackDevicesBatchMessage(const std::string &message);
    void handleRackModeRemoveDeviceContainerMessage(const std::string &message);
    void handleRackModeAddDeviceContainerMessage(const std::string &message);
    void handleAllValuesMessage(const std::string &message);
    void handleVolumeSubModeMessage(const std::string &message);
    void handleMuteBySoloMessage(const std::string &message);
    void handleTrackArmMessage(const std::string &message);
    void handleMuteStateMessage(const std::string &message);
    void handleStartMessage(const std::string &message);
    void handleVolumeModeSubVolumeModeInitMessage(const std::string &message);    
    // Handlers pour les relations entre pistes
    void handleTrackParentMessage(const std::string &message);
    void handleTrackDaughtersMessage(const std::string &message);
    void handleTrackSistersMessage(const std::string &message);
    void handleTrackDaughtersCountMessage(const std::string &message);
    void handleTrackSistersCountMessage(const std::string &message);
    void handleTrackMutViaSoloMessage(const std::string &message);
    void handleSongSyncMessage(const std::string &message);
    void handleSongPositionMessage(const std::string &message);
    void handleSongStateMessage(const std::string &message);
    void handleSongCurrentCueNameMessage(const std::string &message);

    TrackModeData parseTrackModeData(const std::string &message, int startPos);
    SlotModeData parseSlotInitData(const std::string &message, int startPos);

    uint32_t calculateProcessDelay(size_t messageLength)
    {
        uint32_t delay = BASE_PROCESS_DELAY + ((messageLength / 10) * CHAR_PROCESS_DELAY);
        return std::min(delay, MAX_PROCESS_DELAY);
    }
};

#endif // COMMUNICATION_MANAGER_H
