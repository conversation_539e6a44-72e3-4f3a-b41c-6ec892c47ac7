// CommunicationManagerTrackMode.cpp

#include "CommunicationManager.h"
#include "gui.h"
#include "scr_liv_tra/scr_liv_tra.h"
#include "esp_log.h"
#include <string>
#include <cstring>
#include <sstream>
#include <vector>
#include <cstdlib> // Pour strcpy, strtol, strtof
#include <cerrno>  // Pour errno
#include <cctype>  // Pour isspace
#include <stdexcept> // Ajout pour std::stof exceptions

static const char* TAG = "CommManagerTrack";

// Déclarations des fonctions de mise à jour de l'interface (à déplacer dans un header approprié)
#ifdef __cplusplus
extern "C" {
#endif

void update_song_sync(int bar, int beat, int subdivision, float time, float tempo, int sig_num, int sig_denom, int isplaying);
void update_song_position(int bar, int beat, int subdivision, float time);

// --- Autres déclarations extern "C" si nécessaire ---
// Par exemple, celles déjà implicitement utilisées :
void update_track_pan(int32_t panValue);
void init_trackmode_display(bool is_foldable, bool fold_state, bool is_grouped, int muteState, int soloState, int mutedViaSoloState, int armState, int color, const char* displayIndex, const char* formattedTrackNumber, const char* name, int volume, const char* volume_db);
void update_track_mute_button(int muteState);
void update_track_solo_button(int soloState);
void update_track_mute_via_solo(int mutedViaSolo);
void update_track_volume(int32_t volume, const char* volume_db);
void update_track_color(int color_code);
void update_track_name(const char* name);
void update_track_send(int sendIndex, int sendValue, const char* sendDb);
void update_track_container_toplabel(char sendLetter, const char* name);
void update_track_send_names(const char* nameA, const char* nameB, const char* nameC, const char* nameD);
void handle_track_state_view(int state);
void update_track_arm_button(int armState);
void update_track_parent(const char* parentName);
void update_track_daughters_count(int count);
void update_track_sisters_count(int count);
void update_track_popup_label(int buttonIndex, const char* label);
void update_track_lock_button(bool locked);


#ifdef __cplusplus
}
#endif

// Fonction utilitaire pour convertir string en int
int stringToInt(const std::string& str) {
    char* endPtr;
    const char* cstr = str.c_str();
    errno = 0; // Important: réinitialiser errno

    // Tentative de conversion
    long value = strtol(cstr, &endPtr, 10);

    // Vérifications d'erreur
    if (endPtr == cstr) {
        ESP_LOGE(TAG, "stringToInt: Pas de conversion effectuée pour '%s'", cstr);
        return -1;  // Valeur d'erreur, pas de chiffres trouvés
    }
    // Ignorer les espaces blancs finaux
    while (isspace(static_cast<unsigned char>(*endPtr))) {
        endPtr++;
    }
    if (*endPtr != '\0') {
         ESP_LOGE(TAG, "stringToInt: Caractères en trop après le nombre dans '%s'", cstr);
        return -1; // Caractères non valides après le nombre
    }
     if (errno == ERANGE) {
        ESP_LOGE(TAG, "stringToInt: Valeur hors limites (overflow/underflow) pour '%s'", cstr);
        return -1; // Dépassement de capacité
    }
     // Vérifier si la valeur est dans les limites d'un int standard
     if (value < INT_MIN || value > INT_MAX) {
         ESP_LOGE(TAG, "stringToInt: Valeur hors limites pour un int pour '%s'", cstr);
         return -1;
     }


    return static_cast<int>(value);
}

// Nouvelle fonction utilitaire pour convertir string en float sans exceptions
bool stringToFloat(const std::string& str, float& result) {
    char* endPtr;
    const char* cstr = str.c_str();
    errno = 0; // Réinitialiser errno avant l'appel

    result = strtof(cstr, &endPtr);

    // Vérifier les erreurs de conversion
    if (endPtr == cstr) {
        ESP_LOGE(TAG, "stringToFloat: Pas de conversion effectuée pour '%s'", cstr);
        return false; // Pas de chiffres trouvés
    }
    // Ignorer les espaces blancs finaux
    while (isspace(static_cast<unsigned char>(*endPtr))) {
        endPtr++;
    }
    if (*endPtr != '\0') {
         ESP_LOGE(TAG, "stringToFloat: Caractères en trop après le nombre dans '%s'", cstr);
        return false; // Caractères non blancs en trop
    }
    if (errno == ERANGE) {
        // Pour strtof, ERANGE peut signifier overflow ou underflow.
        // Un underflow donnant 0.0 peut être acceptable selon le cas, mais loguons quand même.
        ESP_LOGW(TAG, "stringToFloat: Valeur hors limites (ERANGE) pour '%s'. Résultat: %f", cstr, result);
        // On pourrait décider de retourner false ici si le dépassement est critique.
        // Pour l'instant, on accepte le résultat (inf, -inf, ou 0.0 pour underflow).
        // Si un comportement strict est nécessaire, décommentez : return false;
    }
    // Pas d'autre errno spécifique à vérifier pour strtof outre ERANGE.

    return true; // Conversion réussie (ou résultat acceptable malgré ERANGE)
}

// Fonction utilitaire pour extraire une sous-chaîne
std::string subString(const std::string& str, size_t start, size_t end = std::string::npos) {
    if (start >= str.length()) return "";
    return str.substr(start, end);
}

// Handlers spécifiques au mode Track
void CommunicationManager::handlePanMessage(const std::string& message)
{
      int panValue = stringToInt(subString(message, 3));
      if (panValue >= -50 && panValue <= 50)
      {
            update_track_pan(panValue);
      }
}

void CommunicationManager::handleTrackModeMessage(const std::string& message)
{
      TrackModeData data = parseTrackModeData(message, 3);
      if (data.isValid)
      {
            init_trackmode_display(
                  data.is_foldable,
                  data.fold_state,
                  data.is_grouped,
                  data.muteState,
                  data.soloState,
                  data.mutedViaSoloState,
                  data.armState,
                  data.color,
                  data.displayIndex.c_str(),
                  data.formattedTrackNumber.c_str(),
                  data.name.c_str(),
                  data.volume,
                  data.volume_db.c_str()
            );
      }
}

void CommunicationManager::handleTrackMutMessage(const std::string& message)
{
      if (message.length() == 4)
      {
            int muteState = message[3] - '0'; // Convertit le caractère en entier
            update_track_mute_button(muteState);
      }
}

void CommunicationManager::handleTrackSoloMessage(const std::string& message)
{
      if (message.length() == 4)
      {
            int soloState = message[3] - '0'; // Convertit le caractère en entier
            update_track_solo_button(soloState);
      }
}

void CommunicationManager::handleTrackMutViaSoloMessage(const std::string& message)
{
      if (message.length() == 4)
      {
            int mutedViaSolo = message[3] - '0'; // Convertit le caractère en entier
            update_track_mute_via_solo(mutedViaSolo);
      }
}

void CommunicationManager::handleTrackVolumeMessage(const std::string& message)
{
      if (message.length() >= 4) // "vt" + au moins 1 chiffre + virgule
      {
            size_t commaPos = message.find(',');
            if (commaPos != std::string::npos)
            {
                  int volume = stringToInt(subString(message, 2, commaPos - 2));
                  std::string volume_db = subString(message, commaPos + 1); // Maintenant une String au lieu d'un float
                  update_track_volume(volume, volume_db.c_str());
            }
      }
}

void CommunicationManager::handleTrackColorMessage(const std::string& message)
{
      if (message.length() >= 4)
      {
            int color_code = stringToInt(subString(message, 2));
            update_track_color(color_code);
      }
}

void CommunicationManager::handleTrackNameMessage(const std::string& message)
{
      if (message.length() >= 4)
      {
            // Envoie le nom à partir de l'index 3 jusqu'à la fin
            std::string name = subString(message, 3);
            update_track_name(name.c_str());
      }
}

void CommunicationManager::handleTrackSendMessage(const std::string& message)
{
      if (message.length() >= 4) // Vérifie la longueur minimale ("ts" + lettre + données)
      {
            char sendLetter = message[2]; // Lettre après "ts"
            if (sendLetter >= 'A' && sendLetter <= 'D')
            {
                  int sendIndex = sendLetter - 'A';

                  // Trouve la position de la virgule après la lettre
                  size_t firstComma = message.find(',', 3);
                  if (firstComma != std::string::npos)
                  {
                        size_t secondComma = message.find(',', firstComma + 1);
                        if (secondComma != std::string::npos)
                        {
                              int sendValue = stringToInt(subString(message, firstComma + 1, secondComma - firstComma - 1));
                              std::string sendDb = subString(message, secondComma + 1); // Gardé en String au lieu de convertir en int
                              update_track_send(sendIndex, sendValue, sendDb.c_str());
                        }
                  }
            }
      }
}

void CommunicationManager::handleTrackReturnNameMessage(const std::string& message)
{
      if (message.length() >= 4)
      {
            char sendLetter = message[2];
            std::string name = subString(message, 3);
            update_track_container_toplabel(sendLetter, name.c_str());
      }
}

void CommunicationManager::handleReturnTracksNameMessage(const std::string& message)
{
      std::string names[4];
      size_t startPos = 3; // Commencer après "rn,"
      int nameIndex = 0;

      // Parser les 4 noms séparés par des virgules
      while (startPos < message.length() && nameIndex < 4)
      {
            size_t nextComma = message.find(',', startPos);
            if (nextComma == std::string::npos)
            {
                  names[nameIndex] = subString(message, startPos);
                  break;
            }
            names[nameIndex] = subString(message, startPos, nextComma - startPos);
            startPos = nextComma + 1;
            nameIndex++;
      }

      // Mettre à jour l'interface avec les noms des pistes de retour
      update_track_send_names(
          names[0].c_str(),
          names[1].c_str(),
          names[2].c_str(),
          names[3].c_str());
}


void CommunicationManager::handleTrackStateViewMessage(const std::string& message)
{
    ESP_LOGI(TAG, "handleTrackStateViewMessage");
    if (message.length() >= 3) {
        int state = message[2] - '0';  // Convertit le caractère en nombre
        if (state >= 0 && state <= 2) {
            handle_track_state_view(state);
        } else {
            ESP_LOGE(TAG, "État invalide reçu : %d", state);
        }
    }
}

void CommunicationManager::handleTrackArmMessage(const std::string& message)
{
      if (message.length() >= 3)
      {
            // Convertir le caractère en entier (0 ou 1)
            int armState = message[2] - '0';
            update_track_arm_button(armState);
      }
}

// Fonction utilitaire pour extraire les noms de pistes d'un message
std::vector<std::string> extractTrackNames(const std::string& message, size_t startPos) {
    std::vector<std::string> names;
    size_t pos = startPos;

    while (pos < message.length()) {
        size_t nextComma = message.find(',', pos);
        if (nextComma == std::string::npos) {
            // Dernier élément
            std::string name = message.substr(pos);
            if (!name.empty()) {
                names.push_back(name);
            }
            break;
        }

        std::string name = message.substr(pos, nextComma - pos);
        if (!name.empty()) {
            names.push_back(name);
        }
        pos = nextComma + 1;
    }

    return names;
}

// Handler pour le message de piste parente (tp)
void CommunicationManager::handleTrackParentMessage(const std::string& message) {
    ESP_LOGI(TAG, "Réception d'un message de piste parente: %s", message.c_str());

    if (message.length() <= 3) {
        ESP_LOGE(TAG, "Message de piste parente trop court");
        return;
    }

    // Extraire le nom du parent (tout ce qui suit "tp,")
    std::string parentName = message.substr(3);

    // Mettre à jour l'interface
    update_track_parent(parentName.c_str());
}

// Handler pour le message de comptage des pistes filles (tc)
void CommunicationManager::handleTrackDaughtersCountMessage(const std::string& message) {
    ESP_LOGI(TAG, "Réception d'un message de comptage des pistes filles: %s", message.c_str());

    if (message.length() <= 3) {
        ESP_LOGE(TAG, "Message de comptage des pistes filles trop court");
        return;
    }

    // Extraire le nombre de pistes filles
    int count = stringToInt(message.substr(3));

    // Vérifier que le nombre est valide
    if (count < 0 || count > 64) {
        ESP_LOGE(TAG, "Nombre de pistes filles invalide: %d", count);
        return;
    }

    // Mettre à jour l'interface
    update_track_daughters_count(count);
}

// Handler pour le message de comptage des pistes soeurs (tw)
void CommunicationManager::handleTrackSistersCountMessage(const std::string& message) {
    ESP_LOGI(TAG, "Réception d'un message de comptage des pistes soeurs: %s", message.c_str());

    if (message.length() <= 3) {
        ESP_LOGE(TAG, "Message de comptage des pistes soeurs trop court");
        return;
    }

    // Extraire le nombre de pistes soeurs
    int count = stringToInt(message.substr(3));

    // Vérifier que le nombre est valide
    if (count < 0 || count > 64) {
        ESP_LOGE(TAG, "Nombre de pistes soeurs invalide: %d", count);
        return;
    }

    // Mettre à jour l'interface
    update_track_sisters_count(count);
}

// Handler pour le message de lot de pistes filles (td)
void CommunicationManager::handleTrackDaughtersMessage(const std::string& message) {
    ESP_LOGI(TAG, "Réception d'un message de lot de pistes filles: %s", message.c_str());

    if (message.length() <= 3) { // td,X... au minimum
        ESP_LOGE(TAG, "Message de lot de pistes filles trop court");
        return;
    }

    // Trouver la virgule après le préfixe "td"
    size_t prefixEndComma = message.find(',', 2);
    if (prefixEndComma == std::string::npos || prefixEndComma != 2) { // Doit être "td,"
        ESP_LOGE(TAG, "Format de message de lot de pistes filles invalide (virgule après préfixe attendue à l'index 2)");
        return;
    }

    // Trouver la virgule après startIndex
    size_t startIndexEndComma = message.find(',', prefixEndComma + 1);
    if (startIndexEndComma == std::string::npos) {
        ESP_LOGE(TAG, "Format de message de lot de pistes filles invalide (virgule après startIndex manquante)");
        return;
    }

    // Extraire la chaîne pour startIndex
    std::string startIndexStr = message.substr(prefixEndComma + 1, startIndexEndComma - (prefixEndComma + 1));
    int startIndex = stringToInt(startIndexStr);

    // Vérifier si stringToInt a échoué
    if (startIndex == -1 && !startIndexStr.empty() && startIndexStr != "0" && startIndexStr != "-1") {
         // stringToInt logue déjà une erreur, mais on s'assure de ne pas continuer avec un startIndex invalide
         // qui pourrait résulter d'une chaîne non-numérique pour startIndex.
        ESP_LOGE(TAG, "Échec de la conversion de startIndex pour le lot de pistes filles: '%s'", startIndexStr.c_str());
        return;
    }
    
    // Extraire les noms des pistes
    std::vector<std::string> trackNames = extractTrackNames(message, startIndexEndComma + 1);

    // Mettre à jour les labels un par un
    for (size_t i = 0; i < trackNames.size(); i++) {
        int buttonIndex = startIndex + i;
        if (buttonIndex < 64) { // Assumer une limite de 64 pour les popups, à ajuster si nécessaire
            update_track_popup_label(buttonIndex, trackNames[i].c_str());
        } else {
            ESP_LOGW(TAG, "Index de bouton de popup hors limites: %d (startIndex: %d, i: %zu)", buttonIndex, startIndex, i);
        }
    }
}

// Handler pour le message de lot de pistes soeurs (tx)
void CommunicationManager::handleTrackSistersMessage(const std::string& message) {
    ESP_LOGI(TAG, "Réception d'un message de lot de pistes soeurs: %s", message.c_str());

    if (message.length() <= 3) { // tx,X... au minimum
        ESP_LOGE(TAG, "Message de lot de pistes soeurs trop court");
        return;
    }

    // Trouver la virgule après le préfixe "tx"
    size_t prefixEndComma = message.find(',', 2); 
    if (prefixEndComma == std::string::npos || prefixEndComma != 2) { // Doit être "tx,"
        ESP_LOGE(TAG, "Format de message de lot de pistes soeurs invalide (virgule après préfixe attendue à l'index 2)");
        return;
    }

    // Trouver la virgule après startIndex
    size_t startIndexEndComma = message.find(',', prefixEndComma + 1);
    if (startIndexEndComma == std::string::npos) {
        ESP_LOGE(TAG, "Format de message de lot de pistes soeurs invalide (virgule après startIndex manquante)");
        return;
    }

    // Extraire la chaîne pour startIndex
    std::string startIndexStr = message.substr(prefixEndComma + 1, startIndexEndComma - (prefixEndComma + 1));
    int startIndex = stringToInt(startIndexStr);
    
    // Vérifier si stringToInt a échoué
    if (startIndex == -1 && !startIndexStr.empty() && startIndexStr != "0" && startIndexStr != "-1") {
        // stringToInt logue déjà une erreur.
        ESP_LOGE(TAG, "Échec de la conversion de startIndex pour le lot de pistes soeurs: '%s'", startIndexStr.c_str());
        return;
    }

    // Extraire les noms des pistes
    std::vector<std::string> trackNames = extractTrackNames(message, startIndexEndComma + 1);

    // Mettre à jour les labels un par un
    for (size_t i = 0; i < trackNames.size(); i++) {
        int buttonIndex = startIndex + i;
        if (buttonIndex < 64) { // Assumer une limite de 64 pour les popups, à ajuster si nécessaire
            update_track_popup_label(buttonIndex, trackNames[i].c_str());
        } else {
            ESP_LOGW(TAG, "Index de bouton de popup hors limites: %d (startIndex: %d, i: %zu)", buttonIndex, startIndex, i);
        }
    }
}



void CommunicationManager::handleSongSyncMessage(const std::string& message) {
    ESP_LOGI(TAG, "Traitement du message de synchronisation de piste: %s", message.c_str());

    size_t startPos = message.find(','); // Trouver la première virgule
    if (startPos == std::string::npos) {
        ESP_LOGE(TAG, "Format invalide pour /ss (ou sp?): pas de virgule trouvée. Message: %s", message.c_str());
        return;
    }
    startPos++; // Commencer le parsing APRES la virgule

    std::vector<std::string> args;
    int argCount = 0;
    size_t currentPos = startPos;

    while (currentPos < message.length() && argCount < 8) {
        size_t commaPos = message.find(',', currentPos);
        std::string arg_str;
        if (commaPos == std::string::npos) {
            // Dernier argument
            if (argCount == 7) {
                arg_str = message.substr(currentPos);
                args.push_back(arg_str);
                argCount++;
            } else {
                ESP_LOGE(TAG, "Virgule manquante après l'argument %d dans /ss (ou sp?). Message: %s", argCount, message.c_str());
                return; // Pas assez d'arguments
            }
            break; // Fin du parsing
        } else {
            arg_str = message.substr(currentPos, commaPos - currentPos);
            args.push_back(arg_str);
            currentPos = commaPos + 1;
            argCount++;
        }
        // Log de l'argument extrait
        // ESP_LOGD(TAG, "Arg %d: '%s'", argCount - 1, arg_str.c_str());
    }

    if (argCount != 8) {
        ESP_LOGE(TAG, "Nombre incorrect d'arguments pour /ss (ou sp?): %d (attendu 8). Message: %s", argCount, message.c_str());
        return;
    }

    // Conversion sans exceptions
    int bar = stringToInt(args[0]);
    int beat = stringToInt(args[1]);
    int subdivision = stringToInt(args[2]);
    float time = 0.0f; // Initialiser
    bool time_ok = stringToFloat(args[3], time);
    float tempo = 0.0f; // Initialiser
    bool tempo_ok = stringToFloat(args[4], tempo);
    int sig_num = stringToInt(args[5]);
    int sig_denom = stringToInt(args[6]);
    int isplaying = stringToInt(args[7]);

    // Vérifier les erreurs de conversion pour les entiers (stringToInt retourne -1 en cas d'erreur)
    // ATTENTION: Si -1 est une valeur valide pour certains de ces champs, cette vérification doit être affinée!
    // Pour l'instant, on suppose que -1 indique une erreur de parsing pour tous.
    if (bar == -1 || beat == -1 || subdivision == -1 || sig_num == -1 || sig_denom == -1 || isplaying == -1) {
         ESP_LOGE(TAG, "Erreur de conversion d'un entier dans /ss (ou sp?). Message: %s", message.c_str());
         // Log détaillé des arguments pour débogage
         for(size_t i=0; i<args.size(); ++i) {
             ESP_LOGE(TAG, "Arg[%zu]: '%s'", i, args[i].c_str());
         }
         return; // Ne pas continuer si une conversion a échoué
    }

     // Vérifier les erreurs de conversion pour les flottants
     if (!time_ok || !tempo_ok) {
         ESP_LOGE(TAG, "Erreur de conversion d'un flottant dans /ss (ou sp?). Message: %s", message.c_str());
          // Log détaillé des arguments pour débogage
         for(size_t i=0; i<args.size(); ++i) {
             ESP_LOGE(TAG, "Arg[%zu]: '%s'", i, args[i].c_str());
         }
         return; // Ne pas continuer si une conversion a échoué
     }


    // La validation des marqueurs -1 n'est plus nécessaire ici si -1 indique déjà une erreur de parsing.
    // Si -1 est une valeur valide, la logique de validation devrait être après la vérification d'erreur.

    ESP_LOGD(TAG, "Appel de update_song_sync avec bar=%d, beat=%d, sub=%d, time=%.2f, tempo=%.2f, sig=%d/%d, playing=%d",
             bar, beat, subdivision, time, tempo, sig_num, sig_denom, isplaying);

    update_song_sync(bar, beat, subdivision, time, tempo, sig_num, sig_denom, isplaying);

}

void CommunicationManager::handleSongPositionMessage(const std::string& message) {
    ESP_LOGI(TAG, "Traitement du message de position de piste: %s", message.c_str());

    size_t startPos = message.find(','); // Trouver la première virgule
    if (startPos == std::string::npos) {
        ESP_LOGE(TAG, "Format invalide pour /sb (ou message similaire): pas de virgule trouvée. Message: %s", message.c_str());
        return;
    }
    startPos++; // Commencer le parsing APRES la virgule

    std::vector<std::string> args;
    int argCount = 0;
    size_t currentPos = startPos;

    while (currentPos < message.length() && argCount < 4) {
        size_t commaPos = message.find(',', currentPos);
        std::string arg_str;
        if (commaPos == std::string::npos) {
            // Dernier argument
            if (argCount == 3) {
                arg_str = message.substr(currentPos);
                args.push_back(arg_str);
                argCount++;
            } else {
                ESP_LOGE(TAG, "Virgule manquante après l'argument %d dans /sb (ou message similaire). Message: %s", argCount, message.c_str());
                return; // Pas assez d'arguments
            }
            break; // Fin du parsing
        } else {
            arg_str = message.substr(currentPos, commaPos - currentPos);
            args.push_back(arg_str);
            currentPos = commaPos + 1;
            argCount++;
        }
        // ESP_LOGD(TAG, "Arg %d: '%s'", argCount - 1, arg_str.c_str());
    }


    if (argCount != 4) {
        ESP_LOGE(TAG, "Nombre incorrect d'arguments pour /sb (ou message similaire): %d (attendu 4). Message: %s", argCount, message.c_str());
        return;
    }

    // Conversion sans exceptions
    int bar = stringToInt(args[0]);
    int beat = stringToInt(args[1]);
    int subdivision = stringToInt(args[2]);
    float time = 0.0f; // Initialiser
    bool time_ok = stringToFloat(args[3], time);

    // Vérifier les erreurs de conversion pour les entiers
     // ATTENTION: Si -1 est une valeur valide, cette vérification doit être affinée!
    if (bar == -1 || beat == -1 || subdivision == -1) {
         ESP_LOGE(TAG, "Erreur de conversion d'un entier dans /sb (ou message similaire). Message: %s", message.c_str());
         for(size_t i=0; i<args.size(); ++i) {
             ESP_LOGE(TAG, "Arg[%zu]: '%s'", i, args[i].c_str());
         }
         return;
    }

    // Vérifier les erreurs de conversion pour le flottant
    if (!time_ok) {
        ESP_LOGE(TAG, "Erreur de conversion du temps (flottant) dans /sb (ou message similaire). Message: %s", message.c_str());
         for(size_t i=0; i<args.size(); ++i) {
             ESP_LOGE(TAG, "Arg[%zu]: '%s'", i, args[i].c_str());
         }
        return;
    }

     // La validation des marqueurs -1 n'est plus nécessaire ici si -1 indique déjà une erreur de parsing.

    ESP_LOGD(TAG, "Appel de update_song_position avec bar=%d, beat=%d, sub=%d, time=%.2f",
             bar, beat, subdivision, time);

    update_song_position(bar, beat, subdivision, time);

}

void CommunicationManager::handleSongStateMessage(const std::string& message) {
    ESP_LOGI(TAG, "Traitement du message d'état du morceau: %s", message.c_str());

    // Le message attendu est "ss,<isplaying><Record_Mode><Metronome><Loop><PunchIn><PunchOut>"
    // où chaque paramètre est un seul chiffre (0 ou 1)
    if (message.length() != 9 || message.substr(0, 3) != "ss,") {
        ESP_LOGE(TAG, "Format invalide pour le message d'état du morceau: %s", message.c_str());
        return;
    }

    // Extraire et convertir les caractères en entiers
    int is_playing = message[3] - '0';
    int record_mode = message[4] - '0';
    int metronome = message[5] - '0';
    int loop = message[6] - '0';
    int punch_in = message[7] - '0';
    int punch_out = message[8] - '0';

    // Validation simple (s'assurer que ce sont bien des 0 ou 1)
    if ((is_playing != 0 && is_playing != 1) ||
        (record_mode != 0 && record_mode != 1) ||
        (metronome != 0 && metronome != 1) ||
        (loop != 0 && loop != 1) ||
        (punch_in != 0 && punch_in != 1) ||
        (punch_out != 0 && punch_out != 1)) {
        ESP_LOGE(TAG, "Valeur(s) invalide(s) dans le message d'état du morceau: %s", message.c_str());
        return;
    }

    // Appeler la fonction de mise à jour de l'UI (à implémenter dans scr_liv_tra_update.c)
    update_song_state(is_playing, record_mode, metronome, loop, punch_in, punch_out);
}

// Handler pour le message de nom de cue courante (sc)
void CommunicationManager::handleSongCurrentCueNameMessage(const std::string& message) {
    ESP_LOGI(TAG, "Traitement du message de nom de cue courante: %s", message.c_str());

    // Le message attendu est "sc,<name>"
    if (message.length() <= 3 || message.substr(0, 3) != "sc,") {
        ESP_LOGE(TAG, "Format invalide pour le message de nom de cue: %s", message.c_str());
        return;
    }

    // Extraire le nom de la cue
    std::string cueName = message.substr(3);

    // Appeler la fonction de mise à jour de l'UI (à implémenter dans scr_liv_tra_update.c)
    update_cue_name_display(cueName.c_str());
}