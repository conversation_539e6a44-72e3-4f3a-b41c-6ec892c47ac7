// CommunicationManagerInit.cpp

#include "CommunicationManager.h"
#include "esp_log.h"
#include "esp_system.h"

static const char* TAG = "CommunicationManager";

void CommunicationManager::initializeMessageHandlers()
{
      //===============================================
      // Handlers de messages trackmode
      //===============================================
      messageHandlers["pan"] = &CommunicationManager::handlePanMessage;
      messageHandlers["slt"] = &CommunicationManager::handleTrackModeMessage; 
      messageHandlers["mt"] = &CommunicationManager::handleTrackMutMessage;
      messageHandlers["tv"] = &CommunicationManager::handleTrackMutViaSoloMessage;
      messageHandlers["to"] = &CommunicationManager::handleTrackSoloMessage;
      messageHandlers["vt"] = &CommunicationManager::handleTrackVolumeMessage;
      messageHandlers["ct"] = &CommunicationManager::handleTrackColorMessage;
      messageHandlers["nt"] = &CommunicationManager::handleTrackNameMessage;
      messageHandlers["ts"] = &CommunicationManager::handleTrackSendMessage;
      messageHandlers["tr"] = &CommunicationManager::handleTrackReturnNameMessage;
      messageHandlers["rn"] = &CommunicationManager::handleReturnTracksNameMessage;
      messageHandlers["tq"] = &CommunicationManager::handleTrackStateViewMessage;
      messageHandlers["ta"] = &CommunicationManager::handleTrackArmMessage;      
      // Handlers pour les relations entre pistes
      messageHandlers["tp"] = &CommunicationManager::handleTrackParentMessage;
      messageHandlers["td"] = &CommunicationManager::handleTrackDaughtersMessage;
      messageHandlers["tx"] = &CommunicationManager::handleTrackSistersMessage;
      messageHandlers["tc"] = &CommunicationManager::handleTrackDaughtersCountMessage;
      messageHandlers["tw"] = &CommunicationManager::handleTrackSistersCountMessage;
      messageHandlers["sp"] = &CommunicationManager::handleSongSyncMessage;
      messageHandlers["sb"] = &CommunicationManager::handleSongPositionMessage;
      messageHandlers["ss"] = &CommunicationManager::handleSongStateMessage;
      messageHandlers["sc"] = &CommunicationManager::handleSongCurrentCueNameMessage;
      //===============================================
      // Handlers de messages volumemode
      //===============================================
      messageHandlers["vo"] = &CommunicationManager::handleVolumeMessage;
      messageHandlers["na"] = &CommunicationManager::handleNameMessage;
      messageHandlers["st"] = &CommunicationManager::handleSelectionMessage;
      messageHandlers["co"] = &CommunicationManager::handleColorMessage;
      messageHandlers["mu"] = &CommunicationManager::handleMuteStateMessage;
      messageHandlers["ms"] = &CommunicationManager::handleMuteSoloMessage;
      messageHandlers["so"] = &CommunicationManager::handleSoloMessage;
      messageHandlers["am"] = &CommunicationManager::handleArmMessage;
      messageHandlers["if"] = &CommunicationManager::handleFoldableMessage;
      messageHandlers["sl"] = &CommunicationManager::handleSlotInitMessage;
      messageHandlers["vp"] = &CommunicationManager::handlePageVolumeMessage;
      messageHandlers["es"] = &CommunicationManager::handleEnableSlotMessage;
      messageHandlers["vz"] = &CommunicationManager::handleAllValuesMessage;
      messageHandlers["mk"] = &CommunicationManager::handleMuteBySoloMessage;
      messageHandlers["vi"] = &CommunicationManager::handleVolumeModeSubVolumeModeInitMessage;
      //===============================================
      // Handlers de messages device mode
      //===============================================
      messageHandlers["pd"] = &CommunicationManager::handleDevicePageMessage;
      messageHandlers["pn"] = &CommunicationManager::handleParameterNameMessage;
      messageHandlers["pv"] = &CommunicationManager::handleParameterValueMessage;
      messageHandlers["ld"] = &CommunicationManager::handleLockDeviceMessage;

      messageHandlers["nn"] = &CommunicationManager::handleDeviceBulkParametersDeviceNames;
      messageHandlers["di"] = &CommunicationManager::handleDeviceBulkParametersValues;
      messageHandlers["dp"] = &CommunicationManager::handleDeviceModeTrackPropertiesMessage;
      messageHandlers["ia"] = &CommunicationManager::handleDeviceModeIsActiveMessage;
      messageHandlers["dd"] = &CommunicationManager::handleDevicesRootDevicesMessage;
      messageHandlers["dk"] = &CommunicationManager::handleDevicesRootDevicesBatchMessage;
      messageHandlers["dc"] = &CommunicationManager::handleDevicesRackChainsDisplayMessage;
      messageHandlers["dz"] = &CommunicationManager::handleDevicesDrumRackChainsDisplayMessage;
      messageHandlers["ds"] = &CommunicationManager::handleDeviceModePathStringMessage;
      messageHandlers["de"] = &CommunicationManager::handleDeviceModeRemoveDeviceContainerMessage;
      messageHandlers["du"] = &CommunicationManager::handleDeviceModeAddDeviceContainerMessage;
      messageHandlers["d1"] = &CommunicationManager::handleDevicesRackDevicesMessage;
      messageHandlers["d2"] = &CommunicationManager::handleDevicesRackDevicesBatchMessage;
      messageHandlers["d3"] = &CommunicationManager::handleRackModeRemoveDeviceContainerMessage;
      messageHandlers["d4"] = &CommunicationManager::handleRackModeAddDeviceContainerMessage;

      //===============================================
      // Handlers de messages learn mode
      //===============================================
      messageHandlers["ls"] = &CommunicationManager::handleLearnSlotMessage;
      messageHandlers["lu"] = &CommunicationManager::handleLearnValueMessage;
      messageHandlers["lc"] = &CommunicationManager::handleLearnColorMessage;
      messageHandlers["ln"] = &CommunicationManager::handleLearnNameMessage;
      messageHandlers["lp"] = &CommunicationManager::handleLearnPageMessage;
      messageHandlers["dn"] = &CommunicationManager::handleLearnDeviceNameMessage;
      messageHandlers["lo"] = &CommunicationManager::handleLearnSlotPropertiesMessage;
      messageHandlers["lx"] = &CommunicationManager::handleLearnClearAllSlotsMessage;

      //===============================================
      // Handlers de messages browser mode
      //===============================================
      messageHandlers["bp"] = &CommunicationManager::handleBrowserInfoMessage;
      messageHandlers["b1"] = &CommunicationManager::handleBrowserItemsFirstBatch;
      messageHandlers["b2"] = &CommunicationManager::handleBrowserItemsSecondBatch;
      messageHandlers["bw"] = &CommunicationManager::handleBrowserPreviewMessage;

      //===============================================
      // Handlers de messages system
      //===============================================
      messageHandlers["mo"] = &CommunicationManager::handleSwitchModeMessage;
      messageHandlers["ex"] = &CommunicationManager::handleExitModeMessage;
      messageHandlers["zz"] = &CommunicationManager::handleStartMessage;

      ESP_LOGI(TAG, "Handlers initialisés :");
      for (const auto &handler : messageHandlers)
      {
            ESP_LOGI(TAG, "Handler pour préfixe : %s", handler.first.c_str());
      }
}