#ifndef SCR_LIV_TRA_H
#define SCR_LIV_TRA_H

#include "lvgl.h"

#ifdef __cplusplus
extern "C"
{
#endif

// Constantes
#define DEFAUTPADDING 10
#define LEFTBANDWIDTHINPERCENT 15

      // Structure principale
      typedef struct
      {
            lv_obj_t *screen;
            struct
            {
                  lv_obj_t *background_container;
                  lv_obj_t *title_label;
                  lv_obj_t *tracks_container;
                  lv_obj_t *track_buttons[64];
                  lv_obj_t *track_labels[64];
            } relatives_popup;
            struct
            {
                  lv_obj_t *background_container;
                  lv_obj_t *title_label;
                  lv_obj_t *bar_roller;
                  lv_obj_t *beat_roller;
                  lv_obj_t *subdivision_roller;
                  lv_obj_t *bar_label;
                  lv_obj_t *beat_label;
                  lv_obj_t *subdivision_label;
                  lv_obj_t *ok_button;
                  lv_obj_t *ok_label;
            } loop_length_popup;
                        struct
            {
                  lv_obj_t *band;
                  lv_obj_t *labels[5];
            } left_band;
            struct
            {
                  lv_obj_t *band;
                  struct
                  {
                        lv_obj_t *container;
                        lv_obj_t *top_label;
                        lv_obj_t *bar;
                        lv_obj_t *bottom_label;
                  } sends[4];
            } top_band;


            struct
            {
                  lv_obj_t *band;
                  lv_obj_t *parent_container;
                  lv_obj_t *parent_label;
                  lv_obj_t *sisters_container;
                  lv_obj_t *sisters_label;
                  lv_obj_t *daughter_container;
                  lv_obj_t *daughter_label;
            } relatives_band;
            struct
            {
                  lv_obj_t *band;
                  lv_obj_t *container;
                  lv_obj_t *track_mode_label;
                  lv_obj_t *track_name_label;
                  lv_obj_t *track_number_label;
                  lv_obj_t *track_color_dot;
            } middle_band;
            struct
            {
                  lv_obj_t *band;
                  struct
                  {
                        lv_obj_t *container;
                        lv_obj_t *bar;
                        lv_obj_t *label;
                  } volume;
                  struct
                  {
                        lv_obj_t *container;
                        lv_obj_t *arc;
                        lv_obj_t *label;
                  } pan;
                  struct
                  {
                        lv_obj_t *container;
                        struct
                        {
                              lv_obj_t *btn;
                              lv_obj_t *label;
                        } mute;
                        struct
                        {
                              lv_obj_t *btn;
                              lv_obj_t *label;
                        } solo;
                        struct
                        {
                              lv_obj_t *btn;
                              lv_obj_t *label;
                        } metro;

                        lv_obj_t *arm_btn;
                        lv_obj_t *arm_circle;
                  } buttons;
                  struct {
                        lv_obj_t *container;
                        struct {
                              lv_obj_t *container;
                              lv_obj_t *barLabel;
                              lv_obj_t *beatLabel;
                              lv_obj_t *subdivLabel;
                        } bar_beat_subdiv;
                        struct {
                              lv_obj_t *container;
                              lv_obj_t *minutesLabel;
                              lv_obj_t *secondsLabel;
                        } time;
                        lv_obj_t *bar_beat_subdiv_container;
                  } position;
                  struct {
                        lv_obj_t *container;
                        struct {
                              lv_obj_t *btn;
                              lv_obj_t *label;
                        } add_cue;
                        struct {
                              lv_obj_t *btn;
                              lv_obj_t *label;
                        } loop_start;
                        struct {
                              lv_obj_t *btn;
                              lv_obj_t *label;
                              lv_obj_t *punch_in;
                              lv_obj_t *punch_out;
                        } loop;
                        lv_obj_t *tempo_label;
                  } song_state;
            } bottom_band;
      } scr_liv_tra_t;

      // Variable globale
      extern scr_liv_tra_t ui;

      // Fonction de création
      lv_obj_t *create_scr_liv_tra(void);

      // Gestionnaires d'événements
      void volume_container_event_cb(lv_event_t *e);
      void pan_container_event_cb(lv_event_t *e);
      void mute_button_event_cb(lv_event_t *e);
      void solo_button_event_cb(lv_event_t *e);
      void locked_button_event_cb(lv_event_t *e);
      void middle_band_event_cb(lv_event_t *e);
      void send_container_event_cb(lv_event_t *e);
      void arm_button_event_cb(lv_event_t *e);
      void metro_button_event_cb(lv_event_t *e);
      void loop_button_event_cb(lv_event_t *e);
      void loop_start_button_event_cb(lv_event_t *e);
      void add_cue_button_event_cb(lv_event_t *e);
      void tempo_label_event_cb(lv_event_t *e);

      // Fonctions de création
      void create_send_container(int index, lv_obj_t *parent, const char *top_label, const char *bottom_label);

      // Fonctions de mise à jour
      void update_send_toplabel(char send_letter, const char *new_label);
      void update_track_container_bottomlabel(char container_letter, const char *new_label);
      void update_track_container_bar(char container_letter, int32_t new_value);
      void update_track_volume(int32_t volume_value, const char *volume_db);
      void update_track_pan(int32_t pan_value);
      void init_trackmode_display(bool is_foldable, bool fold_state,
                                bool is_grouped, int muteState, int soloState, int mutedViaSoloState,
                                int armState, int color,
                                const char* displayIndex, const char* formattedTrackNumber, const char* name,
                                int volume, const char* volumeDb);
      void update_track_name(const char *name);
      void update_track_mute_button(int muteState);
      void update_track_solo_button(int soloState);
      void update_track_color(int color_code);
      void update_track_send(int sendIndex, int sendValue, const char *sendDb);
      void update_track_send_names(const char *name1, const char *name2, const char *name3, const char *name4);
      void handle_track_state_view(int state);
      void update_track_arm_button(int armState);
      void update_track_container_toplabel(char container_letter, const char *new_label);
      void update_track_number(const char *trackNumber);
      void update_is_grouped_consequences(bool is_grouped);
      void update_is_foldable_consequences(bool is_foldable);

      // Fonctions pour le popup des tracks sisters/daughters
      void create_relatives_popup(void);
      void show_relatives_popup(void);
      void hide_relatives_popup(void);
      void update_relatives_popup_tracks(const char **track_names, int count);
      void relatives_popup_button_event_cb(lv_event_t *e);
      void track_button_event_cb(lv_event_t *e);
      void sisters_container_event_cb(lv_event_t *e);
      void daughters_container_event_cb(lv_event_t *e);
      void parent_container_event_cb(lv_event_t *e);
      // Fonctions pour les relations entre pistes
      void update_track_parent(const char *parent_name);
      void update_track_daughters_count(int count);
      void update_track_sisters_count(int count);
      void update_track_daughters_batch(int start_index, const char **names, int count);
      void update_track_sisters_batch(int start_index, const char **names, int count);
      // Nouvelle fonction pour mettre à jour un seul label
      void update_track_popup_label(int index, const char *name);
      void create_bottom_band(void);
      void update_track_mute_via_solo(int mutedViaSoloState);
      void update_track_display_index(const char *displayIndex);
      void update_song_sync(int bar, int beat, int subdivision, float time, float tempo, int sig_num, int sig_denom, int isplaying);
      void update_song_position(int bar, int beat, int subdivision, float time);
      void update_song_state(int is_playing, int record_mode, int metronome, int loop, int punch_in, int punch_out);
      void update_cue_name_display(const char* name);

      // Fonctions pour le popup loop length
      void create_loop_length_popup(void);
      void show_loop_length_popup(void);
      void hide_loop_length_popup(void);
      void loop_length_bar_roller_event_cb(lv_event_t *e);
      void loop_length_beat_roller_event_cb(lv_event_t *e);
      void loop_length_subdivision_roller_event_cb(lv_event_t *e);
      void loop_length_ok_button_event_cb(lv_event_t *e);
      void loop_length_popup_background_event_cb(lv_event_t *e);


#ifdef __cplusplus
}
#endif

#endif // SCR_LIV_TRA_H