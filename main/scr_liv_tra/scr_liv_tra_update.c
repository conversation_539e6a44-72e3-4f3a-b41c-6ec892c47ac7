#include "lvgl.h"
#include "../gui.h"
#include "../styles.h"
#include "scr_liv_tra.h"
#include "esp_log.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h> // Pour strdup et free
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_ops.h"
#include "bsp/esp-bsp.h"

static const char *TAG = "SCR_LIV_TRA_UPDATE";

#ifdef __cplusplus
extern "C"
{
#endif
      extern void sendMessage(const char *message);
      extern bool bsp_display_lock(uint32_t timeout_ms);
      extern void bsp_display_unlock(void);
#ifdef __cplusplus
}
#endif

// Structure pour stocker l'état de l'interpolation
typedef struct {
    // Paramètres musicaux
    int bar;
    int beat;
    int subdivision;
    float tempo;
    int sig_num;
    int sig_denom;
    int subdivisions_per_beat;
    TickType_t subdivision_duration_ticks;
    
    // Paramètres de temps
    float current_time;
    
    // État général
    bool is_playing;
    TaskHandle_t interpolation_task_handle;
    TaskHandle_t time_task_handle;
} InterpolationState;

static InterpolationState interpolation_state = {0};

// Fonction pour calculer le nombre de subdivisions par beat
static int calculate_subdivisions_per_beat(int sig_denom) {
    switch (sig_denom) {
        case 1: return 16;
        case 2: return 8;
        case 4: return 4;
        case 8: return 2;
        case 16: return 1;
        default: return 4; // Valeur par défaut
    }
}

// Fonction pour mettre à jour l'affichage de la position
static void update_position_display(int bar, int beat, int subdivision) {
    char bar_text[8], beat_text[8], subdiv_text[8];
    
    snprintf(bar_text, sizeof(bar_text), "%d", bar);
    snprintf(beat_text, sizeof(beat_text), "%d", beat);
    snprintf(subdiv_text, sizeof(subdiv_text), "%d", subdivision);
    
    lv_label_set_text(ui.bottom_band.position.bar_beat_subdiv.barLabel, bar_text);
    lv_label_set_text(ui.bottom_band.position.bar_beat_subdiv.beatLabel, beat_text);
    lv_label_set_text(ui.bottom_band.position.bar_beat_subdiv.subdivLabel, subdiv_text);
}

// Fonction pour mettre à jour l'affichage du temps
static void update_time_display(float time) {
    static int last_minutes = -1;
    static int last_seconds = -1;
    
    int minutes = (int)(time / 60.0f);
    int seconds = (int)time % 60;
    
    // Ne mettre à jour que si les valeurs ont changé
    if (minutes != last_minutes || seconds != last_seconds) {
        char min_text[8], sec_text[8];
        snprintf(min_text, sizeof(min_text), "%d", minutes);
        snprintf(sec_text, sizeof(sec_text), "%d", seconds);
        
        bsp_display_lock(0);
        lv_label_set_text(ui.bottom_band.position.time.minutesLabel, min_text);
        lv_label_set_text(ui.bottom_band.position.time.secondsLabel, sec_text);
        bsp_display_unlock();
        
        last_minutes = minutes;
        last_seconds = seconds;
    }
}

// Tâche de mise à jour du temps
static void time_task(void *pvParameters) {
    InterpolationState *state = (InterpolationState *)pvParameters;
    TickType_t last_wake_time = xTaskGetTickCount();
    const TickType_t update_period = pdMS_TO_TICKS(50); // Mise à jour toutes les 50ms
    
    while (1) {
        if (state->is_playing) {
            state->current_time += 0.05f; // Incrément de 50ms
            update_time_display(state->current_time);
            vTaskDelayUntil(&last_wake_time, update_period);
        } else {
            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }
}

// Tâche d'interpolation des subdivisions
static void interpolation_task(void *pvParameters) {
    InterpolationState *state = (InterpolationState *)pvParameters;
    TickType_t last_wake_time = xTaskGetTickCount();
    
    while (1) {
        if (state->is_playing) {
            state->subdivision++;
            if (state->subdivision > state->subdivisions_per_beat) {
                state->subdivision = 1;
            }
            
            bsp_display_lock(0);
            update_position_display(state->bar, state->beat, state->subdivision);
            bsp_display_unlock();
            
            vTaskDelayUntil(&last_wake_time, state->subdivision_duration_ticks);
        } else {
            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }
}

// Nouvelle fonction vide
void update_track_mute_via_solo(int mutedViaSoloState)
{
    ESP_LOGI(TAG, "Mise à jour du Muted Via Solo: état=%d", mutedViaSoloState);
    // Implémentation future
}

// Fonctions de mise à jour
void update_send_toplabel(char send_letter, const char *new_label)
{
      int send_index = send_letter - 'A';
      if (send_index >= 0 && send_index < 4)
      {
            ESP_LOGI(TAG, "Mise à jour du label supérieur du send %c: %s", send_letter, new_label);
            lv_label_set_text(ui.top_band.sends[send_index].top_label, new_label);
      }
}

void update_track_container_bottomlabel(char container_letter, const char *new_label)
{
      int container_index = container_letter - 'A';
      if (container_index >= 0 && container_index < 4)
      {
            ESP_LOGI(TAG, "Mise à jour du label inférieur du container %c: %s", container_letter, new_label);
            lv_label_set_text(ui.top_band.sends[container_index].bottom_label, new_label);
      }
}

void update_track_container_bar(char container_letter, int32_t new_value)
{
      int container_index = container_letter - 'A';
      if (container_index >= 0 && container_index < 4)
      {
            ESP_LOGI(TAG, "Mise à jour de la barre du container %c: %d", container_letter, (int)new_value);
            lv_bar_set_value(ui.top_band.sends[container_index].bar, new_value, LV_ANIM_OFF);
      }
}

void update_track_volume(int32_t volume_value, const char *volume_db)
{
      ESP_LOGI(TAG, "Mise à jour du volume: %d, %s", (int)volume_value, volume_db);
      lv_bar_set_value(ui.bottom_band.volume.bar, volume_value, LV_ANIM_OFF);
      char volume_text[20];
      snprintf(volume_text, sizeof(volume_text), "%s dB", volume_db);
      lv_label_set_text(ui.bottom_band.volume.label, volume_text);
}

void update_track_pan(int32_t pan_value)
{
      ESP_LOGI(TAG, "Mise à jour du pan: %d", (int)pan_value);
      lv_arc_set_value(ui.bottom_band.pan.arc, pan_value);

      char pan_text[10];
      if (pan_value < 0)
      {
            snprintf(pan_text, sizeof(pan_text), "%dL", -pan_value);
      }
      else if (pan_value > 0)
      {
            snprintf(pan_text, sizeof(pan_text), "%dR", pan_value);
      }
      else
      {
            snprintf(pan_text, sizeof(pan_text), "C");
      }

      lv_label_set_text(ui.bottom_band.pan.label, pan_text);
}

void init_trackmode_display(bool is_foldable, bool fold_state,
                          bool is_grouped, int muteState, int soloState,
                          int mutedViaSoloState, int armState, int color,
                          const char* displayIndex, const char* formattedTrackNumber, const char* name,
                          int volume, const char* volumeDb)
{
      ESP_LOGI(TAG, "Initialisation de l'affichage du mode de piste: track=%s, color=0x%X", name, color);
      update_track_arm_button(armState); 
      update_track_name(name);
      update_track_volume(volume, volumeDb);
      update_track_color(color);
      update_track_mute_button(muteState);
      update_track_solo_button(soloState);
      update_track_mute_via_solo(mutedViaSoloState);  
      update_track_display_index(displayIndex);
      update_track_number(formattedTrackNumber);
      update_is_grouped_consequences(is_grouped);
      update_is_foldable_consequences(is_foldable);
      
}

void update_is_foldable_consequences(bool is_foldable)
{
      if (is_foldable==false)
      {
            lv_obj_add_flag(ui.relatives_band.daughter_container, LV_OBJ_FLAG_HIDDEN);
      }
}

void update_is_grouped_consequences(bool is_grouped)
{
      if (is_grouped==false)
      {
            lv_obj_add_flag(ui.relatives_band.parent_container, LV_OBJ_FLAG_HIDDEN);
            lv_obj_add_flag(ui.relatives_band.sisters_container, LV_OBJ_FLAG_HIDDEN);
      }
}

void update_track_number(const char *formattedTrackNumber)
{
      ESP_LOGI(TAG, "Mise à jour du numéro de piste: %s", formattedTrackNumber);
      lv_label_set_text(ui.middle_band.track_number_label, formattedTrackNumber);
}

void update_track_name(const char *name)
{
      ESP_LOGI(TAG, "Mise à jour du nom de piste: %s", name);
      lv_label_set_text(ui.middle_band.track_name_label, name);
}

void update_track_display_index(const char *displayIndex)
{
      ESP_LOGI(TAG, "Mise à jour de l'index de piste: %s", displayIndex);
      lv_label_set_text(ui.bottom_band.buttons.mute.label, displayIndex);
}

void update_track_mute_button(int muteState)
{
      ESP_LOGI(TAG, "Mise à jour du bouton mute: état=%d", muteState);
      

      // Mise à jour des couleurs selon l'état
      switch (muteState)
      {
      case 0: // État désactivé
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.label, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.mute.btn, COULEUR_ORANGE, 0);
            lv_obj_set_style_text_color(ui.bottom_band.buttons.mute.label, COULEUR_NOIRE, 0);
            break;

      case 1: // État muté
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.label, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.mute.btn, COULEUR_GRIS, 0);
            lv_obj_set_style_text_color(ui.bottom_band.buttons.mute.label, COULEUR_BEIGE, 0);
            break;

      case 2: // État muté via solo
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.label, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.mute.btn, COULEUR_GRIS, 0);
            lv_obj_set_style_text_color(ui.bottom_band.buttons.mute.label, COULEUR_NOIRE, 0);
            break;

      case 3: // État désactivé via solo
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui.bottom_band.buttons.mute.label, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.mute.btn, COULEUR_GRIS, 0);
            lv_obj_set_style_text_color(ui.bottom_band.buttons.mute.label, COULEUR_GRIS, 0);
            break;

      case 4: // État invisible
            lv_obj_add_flag(ui.bottom_band.buttons.mute.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_add_flag(ui.bottom_band.buttons.mute.label, LV_OBJ_FLAG_HIDDEN);
            break;
      }
}

void update_track_solo_button(int soloState)
{
      ESP_LOGI(TAG, "Mise à jour du bouton solo: état=%d", soloState);
      if (soloState == 2)
      {
            // Rendre l'objet invisible
            lv_obj_add_flag(ui.bottom_band.buttons.solo.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_add_flag(ui.bottom_band.buttons.solo.label, LV_OBJ_FLAG_HIDDEN);
      }
      else
      {
            // Rendre l'objet visible
            lv_obj_clear_flag(ui.bottom_band.buttons.solo.btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui.bottom_band.buttons.solo.label, LV_OBJ_FLAG_HIDDEN);

            // Appliquer les couleurs selon l'état
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.solo.btn, soloState ? COULEUR_BLEU : COULEUR_GRIS, 0);
            lv_obj_set_style_text_color(ui.bottom_band.buttons.solo.label, soloState ? COULEUR_NOIRE : COULEUR_BEIGE, 0);
      }
}

void update_track_color(int color_code)
{
      ESP_LOGI(TAG, "Mise à jour de la couleur de piste: 0x%X", color_code);
      lv_color_t color = lv_color_hex(color_code);
      lv_obj_set_style_bg_color(ui.middle_band.track_color_dot, color, 0);
}

void update_track_send(int sendIndex, int sendValue, const char *sendDb)
{
      if (sendIndex >= 0 && sendIndex < 4)
      {
            ESP_LOGI(TAG, "Mise à jour du send %d: valeur=%d, dB=%s", sendIndex, sendValue, sendDb);
            lv_bar_set_value(ui.top_band.sends[sendIndex].bar, sendValue, LV_ANIM_OFF);
            lv_label_set_text(ui.top_band.sends[sendIndex].bottom_label, sendDb);
      }
}

void update_track_send_names(const char *name1, const char *name2, const char *name3, const char *name4)
{
      ESP_LOGI(TAG, "Mise à jour des noms de sends: %s, %s, %s, %s",
            name1, name2, name3, name4);
      const char *names[] = {name1, name2, name3, name4};

      for (int i = 0; i < 4; i++)
      {
            if (strcmp(names[i], "nul") == 0)
            {
                  // Cache le container si le nom est "nul"
                  lv_obj_add_flag(ui.top_band.sends[i].container, LV_OBJ_FLAG_HIDDEN);
            }
            else
            {
                  // Affiche le container et met à jour le label
                  lv_obj_clear_flag(ui.top_band.sends[i].container, LV_OBJ_FLAG_HIDDEN);
                  lv_label_set_text(ui.top_band.sends[i].top_label, names[i]);
            }
      }
}

void handle_track_state_view(int state)
{
      if (state==1)
      {
            ESP_LOGI(TAG, "Passage en mode vue normale non verrouillée");
            // Ajouter les 5 labels dans left_band
            const char *labels[] = {"learn.", "", "main.", "device.", "browser."};
            for (int i = 0; i < 5; i++)
            {
                  lv_label_set_text(ui.left_band.labels[i], labels[i]);
            }
            lv_obj_set_style_bg_color(ui.left_band.band, COULEUR_BEIGE, 0);
      }
      else if (state==2)
      {
            ESP_LOGI(TAG, "Passage en mode vue normale verrouillée");
            // Ajouter les 5 labels dans left_band
            const char *labels[] = {"learn.", "", "main.", "device.", "browser."};
            for (int i = 0; i < 5; i++)
            {
                  lv_label_set_text(ui.left_band.labels[i], labels[i]);
            }
            lv_obj_set_style_bg_color(ui.left_band.band, COULEUR_ORANGE, 0);
      }
      else 
      {
            ESP_LOGI(TAG, "Passage en mode vue rapide");
            const char *labels[] = {"fold.", "", "arm.", "mute.", "solo."};
            for (int i = 0; i < 5; i++)
            {
                  lv_label_set_text(ui.left_band.labels[i], labels[i]);
            }
            lv_obj_set_style_bg_color(ui.left_band.band, COULEUR_BEIGE, 0);
      }
}

void update_track_container_toplabel(char container_letter, const char *new_label)
{
      int container_index = container_letter - 'A';
      if (container_index >= 0 && container_index < 4)
      {
            if (strcmp(new_label, "nul") == 0)
            {
                  ESP_LOGI(TAG, "Masquage du container %c", container_letter);
                  lv_obj_add_flag(ui.top_band.sends[container_index].container, LV_OBJ_FLAG_HIDDEN);
            }
            else
            {
                  ESP_LOGI(TAG, "Mise à jour du label supérieur du container %c: %s", container_letter, new_label);
                  lv_obj_clear_flag(ui.top_band.sends[container_index].container, LV_OBJ_FLAG_HIDDEN);
                  lv_label_set_text(ui.top_band.sends[container_index].top_label, new_label);
            }
      }
}

void update_track_arm_button(int armState)
{
      ESP_LOGI(TAG, "Mise à jour du bouton arm: état=%d", armState);
      if (armState == 0)
      {
            // Désarmé - Couleur grise
            lv_obj_clear_flag(ui.bottom_band.buttons.arm_btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.arm_btn, COULEUR_GRIS, 0);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.arm_circle, COULEUR_BEIGE, 0);
      }
      else if (armState == 1)
      {
            // Armé - Couleur rouge
            lv_obj_clear_flag(ui.bottom_band.buttons.arm_btn, LV_OBJ_FLAG_HIDDEN);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.arm_btn, COULEUR_BORDEAUX, 0);
            lv_obj_set_style_bg_color(ui.bottom_band.buttons.arm_circle, COULEUR_BEIGE, 0);
      }
      else if (armState == 2)
      {
            // Caché
            lv_obj_add_flag(ui.bottom_band.buttons.arm_btn, LV_OBJ_FLAG_HIDDEN);
      }
      else
      {
            // Pour d'autres états, réafficher si nécessaire
            lv_obj_clear_flag(ui.bottom_band.buttons.arm_btn, LV_OBJ_FLAG_HIDDEN);
      }
}

// Function to show the popup
void show_relatives_popup(void) {
      ESP_LOGI(TAG, "Affichage du popup des tracks relatives");
      lv_obj_clear_flag(ui.relatives_popup.background_container, LV_OBJ_FLAG_HIDDEN);
}

// Function to hide the popup
void hide_relatives_popup(void) {
      ESP_LOGI(TAG, "Masquage du popup des tracks relatives");
      lv_obj_add_flag(ui.relatives_popup.background_container, LV_OBJ_FLAG_HIDDEN);
}

// Function to update the track buttons in the popup
void update_relatives_popup_tracks(const char **track_names, int count) {
      ESP_LOGI(TAG, "Mise à jour des %d tracks dans le popup", count);

      // Limit the count to the maximum number of buttons
      if (count > 64) count = 64;

      // Update the track buttons with the provided names
      for (int i = 0; i < count; i++) {
            lv_label_set_text(ui.relatives_popup.track_labels[i], track_names[i]);
            lv_obj_clear_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_HIDDEN);
      }

      // Hide any unused buttons
      for (int i = count; i < 64; i++) {
            lv_obj_add_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_HIDDEN);
      }
}

// Fonctions pour les relations entre pistes
void update_track_parent(const char *parent_name) {
      ESP_LOGI(TAG, "Mise à jour de la piste parente: %s", parent_name);

      // Afficher le conteneur parent s'il était caché
      lv_obj_clear_flag(ui.relatives_band.parent_container, LV_OBJ_FLAG_HIDDEN);

      // Mettre à jour le label avec le nom du parent
      char label_text[50];
      snprintf(label_text, sizeof(label_text), "group track, parent: %s", parent_name);
      lv_label_set_text(ui.relatives_band.parent_label, label_text);
}

// Variables globales pour stocker l'état actuel des pistes filles/soeurs
static int current_daughters_count = 0;
static int current_sisters_count = 0;
static char *track_names_buffer[64] = {NULL}; // Buffer pour stocker les noms des pistes

void update_track_daughters_count(int count) {
      ESP_LOGI(TAG, "Mise à jour du nombre de pistes filles: %d", count);

      // Stocker le nombre de pistes filles
      current_daughters_count = count;

      // Si count > 0, afficher le conteneur des filles, sinon le cacher
      if (count > 0) {
            lv_obj_clear_flag(ui.relatives_band.daughter_container, LV_OBJ_FLAG_HIDDEN);

            // Mettre à jour le label avec le nombre de pistes filles
            char label_text[20];
            snprintf(label_text, sizeof(label_text), "Daughters (%d)", count);
            lv_label_set_text(ui.relatives_band.daughter_label, label_text);

            // Mettre à jour le titre du popup
            lv_label_set_text(ui.relatives_popup.title_label, "Daughters tracks");

            // Afficher seulement les count premiers boutons dans le popup
            for (int i = 0; i < 64; i++) {
                  if (i < count) {
                        lv_obj_clear_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_HIDDEN);
                  } else {
                        lv_obj_add_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_HIDDEN);
                  }
            }
      } else {
            lv_obj_add_flag(ui.relatives_band.daughter_container, LV_OBJ_FLAG_HIDDEN);
      }
}

void update_track_sisters_count(int count) {
      ESP_LOGI(TAG, "Mise à jour du nombre de pistes soeurs: %d", count);

      // Stocker le nombre de pistes soeurs
      current_sisters_count = count;

      // Si count > 0, afficher le conteneur des soeurs, sinon le cacher
      if (count > 0) {
            lv_obj_clear_flag(ui.relatives_band.sisters_container, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui.relatives_band.parent_container, LV_OBJ_FLAG_HIDDEN);

            // Mettre à jour le label avec le nombre de pistes soeurs
            char label_text[20];
            snprintf(label_text, sizeof(label_text), "Sisters (%d)", count);
            lv_label_set_text(ui.relatives_band.sisters_label, label_text);

            // Mettre à jour le titre du popup
            lv_label_set_text(ui.relatives_popup.title_label, "Sisters tracks");

            // Afficher seulement les count premiers boutons dans le popup
            for (int i = 0; i < 64; i++) {
                  if (i < count) {
                        lv_obj_clear_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_HIDDEN);
                  } else {
                        lv_obj_add_flag(ui.relatives_popup.track_buttons[i], LV_OBJ_FLAG_HIDDEN);
                  }
            }
      } else {
            lv_obj_add_flag(ui.relatives_band.sisters_container, LV_OBJ_FLAG_HIDDEN);
      }
}

// Fonction pour mettre à jour un seul label dans le popup
void update_track_popup_label(int index, const char *name) {
      ESP_LOGI(TAG, "Mise à jour du label %d avec le nom: %s", index, name);

      // Vérifier que l'index est valide
      if (index < 0 || index >= 64) {
            ESP_LOGE(TAG, "Index de label invalide: %d", index);
            return;
      }

      // Stocker le nom dans le buffer
      if (track_names_buffer[index] != NULL) {
            free(track_names_buffer[index]);
      }
      track_names_buffer[index] = strdup(name);

      // Mettre à jour le label du bouton
      lv_label_set_text(ui.relatives_popup.track_labels[index], name);

      // S'assurer que le bouton est visible
      lv_obj_clear_flag(ui.relatives_popup.track_buttons[index], LV_OBJ_FLAG_HIDDEN);
}

// Fonction pour arrêter les tâches en cours
static void stop_running_tasks(void) {
    if (interpolation_state.time_task_handle != NULL) {
        vTaskDelete(interpolation_state.time_task_handle);
        interpolation_state.time_task_handle = NULL;
    }
    if (interpolation_state.interpolation_task_handle != NULL) {
        vTaskDelete(interpolation_state.interpolation_task_handle);
        interpolation_state.interpolation_task_handle = NULL;
    }
}

// Fonction pour démarrer les tâches avec un état propre
static void start_tasks(void) {
    // S'assurer qu'aucune tâche n'est en cours
    stop_running_tasks();
    
    // Créer les nouvelles tâches
    if (interpolation_state.is_playing) {
        xTaskCreate(interpolation_task, "interpolation_task", 2048,
                   &interpolation_state, 5, &interpolation_state.interpolation_task_handle);
        xTaskCreate(time_task, "time_task", 2048,
                   &interpolation_state, 5, &interpolation_state.time_task_handle);
    }
}

void update_song_sync(int bar, int beat, int subdivision, float time, float tempo, int sig_num, int sig_denom, int isplaying)
{
    ESP_LOGI(TAG, "Mise à jour de la synchronisation de la chanson: bar=%d, beat=%d, subdivision=%d, time=%f, tempo=%f, sig_num=%d, sig_denom=%d, isplaying=%d", 
             bar, beat, subdivision, time, tempo, sig_num, sig_denom, isplaying);

    // Arrêter les tâches avant toute modification d'état
    stop_running_tasks();

    // Mettre à jour l'état
    interpolation_state.bar = bar;
    interpolation_state.beat = beat;
    interpolation_state.subdivision = subdivision;
    interpolation_state.tempo = tempo;
    interpolation_state.sig_num = sig_num;
    interpolation_state.sig_denom = sig_denom;
    interpolation_state.current_time = time;
    interpolation_state.is_playing = isplaying;
    
    // Calculer les paramètres d'interpolation musicale
    interpolation_state.subdivisions_per_beat = calculate_subdivisions_per_beat(sig_denom);
    float beat_duration_ms = 60000.0f / tempo;
    float subdivision_duration_ms = beat_duration_ms / interpolation_state.subdivisions_per_beat;
    interpolation_state.subdivision_duration_ticks = pdMS_TO_TICKS(subdivision_duration_ms);

    // Mettre à jour l'affichage immédiatement
    bsp_display_lock(0);
    update_position_display(bar, beat, subdivision);
    update_time_display(time);
    
    // Mettre à jour le label de tempo
    char tempo_text[20];
    snprintf(tempo_text, sizeof(tempo_text), "%.1f   %d/%d", tempo, sig_num, sig_denom);
    lv_label_set_text(ui.bottom_band.song_state.tempo_label, tempo_text);
    
    bsp_display_unlock();

    // Démarrer les tâches si nécessaire
    start_tasks();
}

void update_song_position(int bar, int beat, int subdivision, float time)
{
    ESP_LOGI(TAG, "Mise à jour de la position de la piste: bar=%d, beat=%d, subdivision=%d, time=%f", bar, beat, subdivision, time);
    
    // Mettre à jour l'état
    interpolation_state.bar = bar;
    interpolation_state.beat = beat;
    interpolation_state.subdivision = subdivision;
    interpolation_state.current_time = time;
    
    // Mettre à jour l'affichage
    bsp_display_lock(0);
    update_position_display(bar, beat, subdivision);
    update_time_display(time);
    bsp_display_unlock();
}

void cleanup_scr_liv_tra_update(void) {
    ESP_LOGI(TAG, "Nettoyage des ressources de l'écran Live Track");
    
    // Arrêter les tâches
    stop_running_tasks();
    
    // Réinitialiser la structure à zéro
    memset(&interpolation_state, 0, sizeof(InterpolationState));
    
    ESP_LOGI(TAG, "Nettoyage terminé");
}

// Implémentation de la fonction de mise à jour de l'état du morceau (vide pour l'instant)
void update_song_state(int is_playing, int record_mode, int metronome, int loop, int punch_in, int punch_out) {
    ESP_LOGI(TAG, "update_song_state: Called with is_playing=%d, record_mode=%d, metronome=%d, loop=%d, punch_in=%d, punch_out=%d",
             is_playing, record_mode, metronome, loop, punch_in, punch_out);

    // Lock display before updating UI elements
    bsp_display_lock(0);

    // is_playing: fond du ui.bottom_band.position.container entre COULEUR_GRIS (0) et COULEUR_VERT (1)
    if (is_playing == 1) {
        lv_obj_set_style_bg_color(ui.bottom_band.position.bar_beat_subdiv.container, COULEUR_VERT, 0);
    } else {
        lv_obj_set_style_bg_color(ui.bottom_band.position.bar_beat_subdiv.container, COULEUR_GRIS, 0);
    }

    // record_mode: time_container fond COULEUR_GRIS (0) et COULEUR_BORDEAUX (1)
    if (record_mode == 1) {
        lv_obj_set_style_bg_color(ui.bottom_band.position.time.container, COULEUR_BORDEAUX, 0);
    } else {
        lv_obj_set_style_bg_color(ui.bottom_band.position.time.container, COULEUR_GRIS, 0);
    }

    // metronome: ui.bottom_band.buttons.metro.btn fond COULEUR_GRIS (0) et COULEUR_BORDEAUX (1)
    if (metronome == 1) {
        lv_obj_set_style_bg_color(ui.bottom_band.buttons.metro.btn, COULEUR_BORDEAUX, 0);
    } else {
        lv_obj_set_style_bg_color(ui.bottom_band.buttons.metro.btn, COULEUR_GRIS, 0);
    }

    // loop: ui.bottom_band.song_state.loop.btn fond COULEUR_GRIS (0) et COULEUR_BLEU (1)
    if (loop == 1) {
        lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.btn, COULEUR_BLEU, 0);
    } else {
        lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.btn, COULEUR_GRIS, 0);
    }

    // punch_in: ui.bottom_band.song_state.loop.punch_in fond COULEUR_GRIS (0) et COULEUR_BLEU (1)
    if (punch_in == 1) {
        lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.punch_in, COULEUR_BLEU, 0);
    } else {
        lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.punch_in, COULEUR_GRIS, 0);
    }

    // punch_out: ui.bottom_band.song_state.loop.punch_out fond COULEUR_GRIS (0) et COULEUR_BLEU (1)
    if (punch_out == 1) {
        lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.punch_out, COULEUR_BLEU, 0);
    } else {
        lv_obj_set_style_bg_color(ui.bottom_band.song_state.loop.punch_out, COULEUR_GRIS, 0);
    }

    bsp_display_unlock();
}

// Implémentation de la fonction de mise à jour du nom de la cue
void update_cue_name_display(const char* name) {
    ESP_LOGI(TAG, "Mise à jour du nom de la cue: %s", name);
    // Mettre à jour le label ui.bottom_band.song_state.add_cue.label
    if (bsp_display_lock(0)) {
        lv_label_set_text(ui.bottom_band.song_state.add_cue.label, name);
        bsp_display_unlock();
    } else {
        ESP_LOGE(TAG, "Impossible de locker l'affichage pour update_cue_name_display");
    }
}

